# 🔍 Guide de Debug - Extraction Online-Fix

## 📋 Messages de Debug Ajoutés

J'ai ajouté des prints de debug détaillés dans le launcher pour identifier exactement où le problème se situe.

## 🎯 Test à Effectuer

### **1. Relancer le Launcher**
- Ferme complètement le launcher actuel
- Relance-le pour que les nouveaux prints soient actifs

### **2. Télécharger Content Warning**
- Lance un nouveau téléchargement de Content Warning
- **Regarde attentivement la console** pendant et après le téléchargement

## 📢 Messages de Debug à Chercher

### **Phase 1 : Vérification Initiale**
```
🔍 DEBUG: Vérification extraction - Status: complete
🔍 DEBUG: Game data: {'official_name': 'Content Warning', 'type': 'Online-Fix.Me', ...}
🔍 DEBUG: Type du jeu: 'Online-Fix.Me'
🔍 DEBUG: is_online_fix_game() retourne: True
```

### **Phase 2 : Démarrage Extraction**
```
✅ Jeu online-fix détecté - Démarrage de l'extraction automatique...
📁 DEBUG: Dossier de téléchargement: C:\path\to\download
🚀 DEBUG: Appel de auto_extract_online_fix()...
```

### **Phase 3 : Recherche Archives**
```
🔍 DEBUG: auto_extract_online_fix() appelée
🔍 DEBUG: download_folder = C:\path\to\download
🔍 DEBUG: Recherche des archives RAR dans C:\path\to\download
🔍 DEBUG: find_rar_archives() - Dossier: C:\path\to\download
✅ DEBUG: Le dossier existe, recherche récursive...
🔍 DEBUG: Scan dossier: C:\path\to\download
🔍 DEBUG: Fichiers trouvés: ['Content.Warning.v1.17.b-OFME.rar', ...]
🔍 DEBUG: Vérification fichier: Content.Warning.v1.17.b-OFME.rar
✅ DEBUG: Archive RAR trouvée: C:\path\to\download\Content.Warning.v1.17.b-OFME.rar
```

### **Phase 4 : Sélection Archive**
```
✅ DEBUG: Archives RAR trouvées: 1
   - C:\path\to\download\Content.Warning.v1.17.b-OFME.rar
🔍 DEBUG: Recherche de l'archive principale...
✅ DEBUG: Archive principale: Content.Warning.v1.17.b-OFME.rar
```

### **Phase 5 : Extraction**
```
🔍 DEBUG: Appel de extract_online_fix_archive()...
🔍 DEBUG: extract_online_fix_archive() appelée
🔍 DEBUG: rar_file = C:\path\to\download\Content.Warning.v1.17.b-OFME.rar
🔍 DEBUG: extract_to = C:\path\to\download
🔍 DEBUG: rarfile.UNRAR_TOOL = C:\Program Files\WinRAR\WinRAR.exe
🔍 DEBUG: Mot de passe utilisé: online-fix.me
✅ DEBUG: Fichier RAR existe, taille: 543334400 bytes
🔍 DEBUG: Ouverture du fichier RAR avec rarfile...
✅ DEBUG: Fichier RAR ouvert avec succès
✅ DEBUG: Contenu de l'archive: 150 fichier(s)
   - Content Warning.exe
   - data/file1.dat
   - data/file2.dat
   ... et 147 autres
🔍 DEBUG: Configuration du mot de passe...
✅ DEBUG: Mot de passe configuré
🔍 DEBUG: Extraction vers C:\path\to\download...
✅ DEBUG: Extraction terminée avec succès
```

### **Phase 6 : Finalisation**
```
✅ DEBUG: extract_online_fix_archive() retourne: True
✅ DEBUG: Extraction réussie !
🔍 DEBUG: Résultat extraction: True
✅ DEBUG: Extraction réussie !
```

## 🚨 Messages d'Erreur Possibles

### **Erreur 1 : Type Non Détecté**
```
🔍 DEBUG: Type du jeu: 'Action'  (ou autre type)
🔍 DEBUG: is_online_fix_game() retourne: False
⏭️  DEBUG: Pas d'extraction - Status: complete, Online-fix: False
```
**Solution** : Le type dans la DB n'est pas exactement `Online-Fix.Me`

### **Erreur 2 : Dossier Non Trouvé**
```
❌ DEBUG: Le dossier C:\path\to\download n'existe pas
🔍 DEBUG: Total archives RAR trouvées: 0
❌ DEBUG: Aucune archive RAR trouvée
```
**Solution** : Problème avec le chemin de téléchargement

### **Erreur 3 : Archive Non Trouvée**
```
✅ DEBUG: Le dossier existe, recherche récursive...
🔍 DEBUG: Fichiers trouvés: ['other_file.txt', ...]
🔍 DEBUG: Total archives RAR trouvées: 0
```
**Solution** : Le fichier RAR n'est pas dans le bon dossier

### **Erreur 4 : WinRAR Non Fonctionnel**
```
❌ DEBUG: Impossible d'exécuter unrar. WinRAR: C:\Program Files\WinRAR\WinRAR.exe
```
**Solution** : Problème avec WinRAR

### **Erreur 5 : Mot de Passe Incorrect**
```
❌ DEBUG: Mot de passe incorrect pour C:\path\to\file.rar
```
**Solution** : L'archive n'utilise pas le mot de passe `online-fix.me`

## 🎯 Actions Selon les Messages

### **Si tu vois Phase 1 mais pas Phase 2**
→ Le type n'est pas détecté correctement

### **Si tu vois Phase 2 mais pas Phase 3**
→ Problème avec la fonction `auto_extract_online_fix()`

### **Si tu vois Phase 3 mais aucune archive trouvée**
→ Le fichier RAR n'est pas au bon endroit

### **Si tu vois Phase 4 mais échec Phase 5**
→ Problème avec WinRAR ou le mot de passe

## 📝 Informations à Noter

Quand tu fais le test, note :

1. **Quels messages apparaissent** dans la console
2. **Où ça s'arrête** dans le processus
3. **Messages d'erreur spécifiques** s'il y en a
4. **Chemin du dossier de téléchargement** affiché

## 🚀 Prêt pour le Test !

Maintenant, relance le launcher et télécharge Content Warning. Les messages de debug vont nous dire exactement où ça bloque ! 🔍

**Important** : Garde la console ouverte et lis attentivement tous les messages qui s'affichent.
