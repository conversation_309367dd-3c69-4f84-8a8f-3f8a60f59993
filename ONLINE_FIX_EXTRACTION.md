# 🔓 Extraction Automatique Online-Fix

## 📋 Description

Le Cracken Launcher intègre maintenant une fonctionnalité d'extraction automatique pour les jeux **online-fix**. Après le téléchargement d'un jeu online-fix, le launcher détecte automatiquement les archives RAR et les extrait avec le mot de passe approprié.

## ⚙️ Fonctionnement

### 1. **Détection Automatique**
Le launcher détecte automatiquement les jeux online-fix basé sur :
- Le nom du jeu
- La description
- Le type de jeu

**Mots-clés détectés :**
- `online-fix`
- `onlinefix`
- `online fix`
- `multiplayer fix`
- `mp fix`
- `lan fix`
- `steamworks fix`
- `steam fix`

### 2. **Processus d'Extraction**
1. ✅ **Téléchargement terminé** avec succès
2. 🔍 **Détection** du jeu online-fix
3. 📁 **Recherche** des archives RAR dans le dossier
4. 🎯 **Sélection** de l'archive principale (part1)
5. 🔓 **Extraction** avec mot de passe `online-fix.me`
6. ✅ **Mise à jour** du statut de progression

### 3. **Sélection d'Archive Intelligente**
Le launcher sélectionne automatiquement la bonne archive :
- **Priorité 1** : Fichiers avec `part1` ou `part01`
- **Priorité 2** : Fichiers sans numéro de partie
- **Priorité 3** : Premier fichier RAR trouvé

**Exemples :**
```
game.part1.rar  ← Sélectionné
game.part2.rar
game.part3.rar

game.part01.rar ← Sélectionné
game.part02.rar

game.rar        ← Sélectionné (archive unique)
```

## 🛠️ Configuration Requise

### **Modules Python**
```bash
pip install rarfile
```

### **Logiciel d'Extraction**
- **Windows** : WinRAR installé
- **Linux** : `unrar` installé
- **macOS** : `unrar` installé

### **Installation WinRAR**
1. Télécharger WinRAR depuis [winrar.com](https://www.winrar.com)
2. Installer avec les paramètres par défaut
3. Le launcher détectera automatiquement WinRAR

## 🎮 Utilisation

### **Téléchargement Normal**
1. Ouvrir le store du launcher
2. Chercher un jeu online-fix
3. Cliquer sur "Télécharger"
4. Choisir le dossier de destination
5. **Attendre la fin du téléchargement**
6. **L'extraction se lance automatiquement !**

### **Statuts d'Extraction**
- `Recherche des archives...` - Scan du dossier
- `Extraction de game.part1.rar...` - Extraction en cours
- `Extraction terminée !` - Succès
- `Téléchargement et extraction terminés !` - Processus complet

### **En Cas d'Erreur**
- `Erreur: Mot de passe incorrect` - Archive non online-fix
- `Erreur: WinRAR non trouvé` - Installer WinRAR
- `Aucune archive trouvée` - Pas d'archives RAR
- `Archive principale non trouvée` - Structure inhabituelle

## 📝 Exemples de Jeux

### **Jeux Détectés Automatiquement**
```json
{
  "cod_mw": {
    "official_name": "Call of Duty Modern Warfare Online-Fix",
    "description": "Jeu de tir multijoueur avec support LAN",
    "type": "FPS"
  },
  "fifa23_fix": {
    "official_name": "FIFA 23 Steamworks Fix",
    "description": "Simulation de football avec fix multijoueur",
    "type": "Sport"
  }
}
```

### **Jeux Non Détectés**
```json
{
  "witcher3": {
    "official_name": "The Witcher 3",
    "description": "RPG solo épique",
    "type": "RPG"
  }
}
```

## 🔧 Dépannage

### **Problème : Module rarfile non trouvé**
```bash
pip install rarfile
```

### **Problème : WinRAR non détecté**
1. Vérifier l'installation de WinRAR
2. Redémarrer le launcher
3. Essayer avec un autre jeu

### **Problème : Mot de passe incorrect**
- Le jeu n'est peut-être pas un vrai online-fix
- Vérifier que le mot de passe est bien `online-fix.me`

### **Problème : Archive non trouvée**
- Vérifier que le téléchargement est complet
- Chercher manuellement les fichiers .rar
- Extraire manuellement si nécessaire

## 🚀 Avantages

### **Automatisation Complète**
- ✅ Détection automatique
- ✅ Sélection intelligente d'archive
- ✅ Extraction avec bon mot de passe
- ✅ Mise à jour du statut

### **Gain de Temps**
- Plus besoin d'extraire manuellement
- Plus besoin de chercher le bon fichier
- Plus besoin de taper le mot de passe

### **Fiabilité**
- Détection basée sur plusieurs critères
- Gestion des erreurs complète
- Compatibilité avec différents formats

## 📊 Statistiques

### **Formats Supportés**
- ✅ Archives RAR multi-parties
- ✅ Archives RAR uniques
- ✅ Mot de passe `online-fix.me`

### **Détection**
- ✅ 8 mots-clés différents
- ✅ Recherche dans nom, description, type
- ✅ Insensible à la casse

### **Extraction**
- ✅ Sélection automatique part1
- ✅ Fallback intelligent
- ✅ Gestion d'erreurs complète

---

## 🎯 Prêt à Utiliser !

La fonctionnalité d'extraction automatique online-fix est maintenant intégrée dans le Cracken Launcher. Elle fonctionne de manière transparente et automatique pour tous les jeux online-fix détectés.

**Mot de passe utilisé :** `online-fix.me`
