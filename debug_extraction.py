#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de debug pour diagnostiquer pourquoi l'extraction n'a pas fonctionné
"""

import os
import json

def debug_extraction_issue():
    """Debug l'extraction qui n'a pas fonctionné"""
    
    print("🔍 DIAGNOSTIC - Extraction Non Effectuée")
    print("=" * 60)
    
    # 1. Vérifier la fonction de détection
    def is_online_fix_game(game_data):
        game_type = game_data.get('type', '')
        return game_type == 'Online-Fix.Me'
    
    # 2. Tester différents types possibles
    print("1. Test de détection avec différents types:")
    
    possible_types = [
        "Online-Fix.Me",      # Type exact attendu
        "online-fix.me",      # Minuscules
        "Online-Fix",         # Sans .Me
        "OFME",              # Abréviation
        "Online-Fix.Me ",    # Avec espace
        " Online-Fix.Me",    # Avec espace au début
        "Online Fix Me",     # Avec espaces
        "Action",            # Type normal
    ]
    
    for test_type in possible_types:
        game_data = {"type": test_type}
        detected = is_online_fix_game(game_data)
        status = "✅ EXTRAIRE" if detected else "❌ IGNORER"
        print(f"   Type: '{test_type}' → {status}")
    
    print(f"\n2. Analyse du fichier téléchargé:")
    print(f"   Nom: Content.Warning.v1.17.b-OFME.rar")
    print(f"   Taille: 530.620 Ko (~530 MB)")
    print(f"   Extension: .rar ✅")
    print(f"   Contient 'OFME': ✅ (Online-Fix.Me)")
    
    print(f"\n3. Causes possibles du problème:")
    print(f"   ❓ Type dans la DB différent de 'Online-Fix.Me'")
    print(f"   ❓ Erreur lors de l'extraction (WinRAR manquant)")
    print(f"   ❓ Mot de passe incorrect")
    print(f"   ❓ Archive corrompue")
    print(f"   ❓ Fonction d'extraction non appelée")
    
    return True

def create_test_extraction_script():
    """Crée un script pour tester l'extraction manuellement"""
    
    print(f"\n🧪 SCRIPT DE TEST MANUEL")
    print("=" * 60)
    
    test_script = '''
# Test manuel d'extraction
import rarfile
import os

def test_manual_extraction():
    rar_file = r"C:\\path\\to\\Content.Warning.v1.17.b-OFME.rar"
    extract_to = r"C:\\path\\to\\extract"
    password = "online-fix.me"
    
    try:
        print("🔓 Test d'extraction manuelle...")
        
        with rarfile.RarFile(rar_file) as rf:
            rf.setpassword(password)
            
            # Lister le contenu
            files = rf.namelist()
            print(f"📋 Contenu: {len(files)} fichier(s)")
            
            # Extraire
            rf.extractall(extract_to)
            print("✅ Extraction réussie !")
            
    except rarfile.RarWrongPassword:
        print("❌ Mot de passe incorrect")
    except rarfile.RarCannotExec:
        print("❌ WinRAR/unrar non trouvé")
    except Exception as e:
        print(f"❌ Erreur: {e}")

# Lancer le test
test_manual_extraction()
'''
    
    print("Script de test manuel:")
    print(test_script)
    
    return test_script

def check_launcher_logs():
    """Vérifie les logs du launcher pour des indices"""
    
    print(f"\n📋 VÉRIFICATIONS À FAIRE")
    print("=" * 60)
    
    checks = [
        "1. Vérifier le type du jeu dans la base de données",
        "2. Vérifier que WinRAR est installé",
        "3. Vérifier les logs de la console du launcher",
        "4. Tester l'extraction manuelle avec le script",
        "5. Vérifier que le module rarfile est bien installé"
    ]
    
    for check in checks:
        print(f"   {check}")
    
    print(f"\n🔧 COMMANDES DE VÉRIFICATION:")
    print(f"   python -c \"import rarfile; print('rarfile OK')\"")
    print(f"   rar  # Vérifier WinRAR en ligne de commande")
    
    print(f"\n📝 INFORMATIONS NÉCESSAIRES:")
    print(f"   - Quel est le type exact du jeu dans la DB ?")
    print(f"   - Y a-t-il des messages d'erreur dans la console ?")
    print(f"   - WinRAR est-il installé sur le système ?")

def suggest_fixes():
    """Suggère des corrections possibles"""
    
    print(f"\n🔧 CORRECTIONS POSSIBLES")
    print("=" * 60)
    
    fixes = [
        {
            "problème": "Type incorrect dans la DB",
            "solution": "Changer le type vers 'Online-Fix.Me' exactement",
            "action": "Mettre à jour la base de données JSONBin"
        },
        {
            "problème": "WinRAR non installé",
            "solution": "Installer WinRAR depuis winrar.com",
            "action": "Télécharger et installer WinRAR"
        },
        {
            "problème": "Module rarfile manquant",
            "solution": "pip install rarfile",
            "action": "Installer le module Python"
        },
        {
            "problème": "Fonction non appelée",
            "solution": "Vérifier les logs de la console",
            "action": "Regarder si 'Jeu online-fix détecté' apparaît"
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        print(f"{i}. {fix['problème']}")
        print(f"   Solution: {fix['solution']}")
        print(f"   Action: {fix['action']}")
        print()

if __name__ == "__main__":
    print("🚀 CRACKEN LAUNCHER - DEBUG EXTRACTION")
    print("=" * 70)
    print("Diagnostic pour comprendre pourquoi l'extraction n'a pas fonctionné")
    print()
    
    # Lancer le diagnostic
    debug_extraction_issue()
    create_test_extraction_script()
    check_launcher_logs()
    suggest_fixes()
    
    print("=" * 70)
    print("🎯 PROCHAINES ÉTAPES:")
    print("1. Vérifier le type du jeu dans la base de données")
    print("2. Regarder les logs de la console du launcher")
    print("3. Tester l'extraction manuelle")
    print("4. Vérifier l'installation de WinRAR")
    
    input("\nAppuyez sur Entrée pour continuer...")
