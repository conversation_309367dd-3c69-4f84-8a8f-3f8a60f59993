#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de debug pour identifier le vrai problème d'extraction
"""

import os
import sys

def test_detection_correct():
    """Test avec le bon type Online-Fix.Me"""
    
    print("🔍 TEST DE DÉTECTION CORRECT")
    print("=" * 50)
    
    def is_online_fix_game(game_data):
        game_type = game_data.get('type', '')
        return game_type == 'Online-Fix.Me'
    
    # Test avec Content Warning
    content_warning = {
        "official_name": "Content Warning",
        "type": "Online-Fix.Me"  # Type exact de la DB
    }
    
    detected = is_online_fix_game(content_warning)
    print(f"Content Warning (type: 'Online-Fix.Me')")
    print(f"Détection: {'✅ OUI' if detected else '❌ NON'}")
    
    return detected

def check_rarfile_installation():
    """Vérifie l'installation du module rarfile"""
    
    print(f"\n🔧 VÉRIFICATION MODULE RARFILE")
    print("=" * 50)
    
    try:
        import rarfile
        print("✅ Module rarfile importé avec succès")
        print(f"Version: {getattr(rarfile, '__version__', 'inconnue')}")
        
        # Test de création d'un objet RarFile
        try:
            # Créer un fichier RAR factice pour tester
            test_rar_path = "test_fake.rar"
            with open(test_rar_path, 'wb') as f:
                f.write(b'Rar!\x1a\x07\x00')  # Signature RAR
            
            # Tenter d'ouvrir avec rarfile
            try:
                rf = rarfile.RarFile(test_rar_path)
                print("✅ Création d'objet RarFile réussie")
                rf.close()
            except Exception as e:
                print(f"❌ Erreur création RarFile: {e}")
            
            # Nettoyer
            if os.path.exists(test_rar_path):
                os.remove(test_rar_path)
                
        except Exception as e:
            print(f"❌ Erreur test RarFile: {e}")
        
        return True
        
    except ImportError:
        print("❌ Module rarfile non trouvé")
        print("💡 Installez avec: pip install rarfile")
        return False

def check_winrar_installation():
    """Vérifie l'installation de WinRAR"""
    
    print(f"\n🗜️  VÉRIFICATION WINRAR")
    print("=" * 50)
    
    import subprocess
    
    # Tester différentes commandes
    commands_to_test = ["rar", "winrar", "unrar"]
    
    for cmd in commands_to_test:
        try:
            result = subprocess.run([cmd], capture_output=True, text=True, timeout=5)
            print(f"✅ Commande '{cmd}' trouvée")
            return True
        except FileNotFoundError:
            print(f"❌ Commande '{cmd}' non trouvée")
        except subprocess.TimeoutExpired:
            print(f"✅ Commande '{cmd}' trouvée (timeout OK)")
            return True
        except Exception as e:
            print(f"⚠️  Commande '{cmd}': {e}")
    
    print("❌ Aucune commande RAR trouvée")
    print("💡 Installez WinRAR depuis winrar.com")
    return False

def test_manual_extraction():
    """Test d'extraction manuelle du fichier Content Warning"""
    
    print(f"\n🧪 TEST D'EXTRACTION MANUELLE")
    print("=" * 50)
    
    # Chemins possibles pour le fichier
    possible_paths = [
        r"C:\Users\<USER>\Downloads\Content.Warning.v1.17.b-OFME.rar",
        r"C:\Downloads\Content.Warning.v1.17.b-OFME.rar",
        r".\Content.Warning.v1.17.b-OFME.rar"
    ]
    
    rar_file = None
    for path in possible_paths:
        if os.path.exists(path):
            rar_file = path
            break
    
    if not rar_file:
        print("❌ Fichier Content.Warning.v1.17.b-OFME.rar non trouvé")
        print("📁 Chemins testés:")
        for path in possible_paths:
            print(f"   - {path}")
        print("💡 Modifiez le chemin dans le script si nécessaire")
        return False
    
    print(f"✅ Fichier trouvé: {rar_file}")
    print(f"📊 Taille: {os.path.getsize(rar_file) / (1024*1024):.1f} MB")
    
    try:
        import rarfile
        
        print("🔓 Tentative d'extraction...")
        
        with rarfile.RarFile(rar_file) as rf:
            # Lister le contenu
            files = rf.namelist()
            print(f"📋 Archive contient {len(files)} fichier(s)")
            
            # Afficher quelques fichiers
            for i, filename in enumerate(files[:3]):
                print(f"   - {filename}")
            if len(files) > 3:
                print(f"   ... et {len(files) - 3} autres")
            
            # Tenter extraction avec mot de passe
            import tempfile
            with tempfile.TemporaryDirectory() as temp_dir:
                try:
                    rf.setpassword("online-fix.me")
                    rf.extractall(temp_dir)
                    print("✅ Extraction réussie avec mot de passe 'online-fix.me'")
                    
                    # Compter les fichiers extraits
                    extracted_count = 0
                    for root, dirs, files in os.walk(temp_dir):
                        extracted_count += len(files)
                    
                    print(f"📁 {extracted_count} fichier(s) extrait(s)")
                    return True
                    
                except rarfile.RarWrongPassword:
                    print("❌ Mot de passe 'online-fix.me' incorrect")
                    
                    # Essayer sans mot de passe
                    try:
                        rf.setpassword("")
                        rf.extractall(temp_dir)
                        print("✅ Extraction réussie SANS mot de passe")
                        return True
                    except:
                        print("❌ Extraction échouée même sans mot de passe")
                        return False
                        
    except Exception as e:
        print(f"❌ Erreur lors de l'extraction: {e}")
        return False

def check_launcher_integration():
    """Vérifie l'intégration dans le launcher"""
    
    print(f"\n🔗 VÉRIFICATION INTÉGRATION LAUNCHER")
    print("=" * 50)
    
    print("Points à vérifier dans le launcher:")
    print("1. ✅ Fonction is_online_fix_game() modifiée")
    print("2. ✅ Fonction auto_extract_online_fix() présente")
    print("3. ✅ Intégration dans le workflow de téléchargement")
    print("4. ❓ Messages de debug dans la console")
    
    print(f"\nMessages à chercher dans la console du launcher:")
    print("   - 'Jeu online-fix détecté - Démarrage de l'extraction automatique...'")
    print("   - 'Recherche des archives...'")
    print("   - 'Archive principale: Content.Warning.v1.17.b-OFME.rar'")
    print("   - 'Extraction en cours...'")
    print("   - 'Extraction terminée !'")
    
    print(f"\n💡 Si ces messages n'apparaissent pas:")
    print("   - Le type dans la DB n'est peut-être pas exactement 'Online-Fix.Me'")
    print("   - La fonction de détection n'est pas appelée")
    print("   - Il y a une erreur silencieuse")

def main():
    """Fonction principale de debug"""
    
    print("🚀 CRACKEN LAUNCHER - DEBUG EXTRACTION RÉEL")
    print("=" * 70)
    print("Diagnostic approfondi du problème d'extraction")
    print()
    
    # Tests séquentiels
    detection_ok = test_detection_correct()
    rarfile_ok = check_rarfile_installation()
    winrar_ok = check_winrar_installation()
    extraction_ok = test_manual_extraction()
    
    check_launcher_integration()
    
    print("\n" + "="*70)
    print("📋 RÉSUMÉ DU DIAGNOSTIC:")
    print(f"   Détection type: {'✅' if detection_ok else '❌'}")
    print(f"   Module rarfile: {'✅' if rarfile_ok else '❌'}")
    print(f"   WinRAR installé: {'✅' if winrar_ok else '❌'}")
    print(f"   Extraction manuelle: {'✅' if extraction_ok else '❌'}")
    
    if detection_ok and rarfile_ok and winrar_ok and extraction_ok:
        print("\n✅ TOUS LES COMPOSANTS FONCTIONNENT")
        print("🔍 Le problème est probablement dans l'intégration du launcher")
        print("💡 Vérifiez les messages de la console lors du téléchargement")
    else:
        print("\n❌ PROBLÈMES DÉTECTÉS")
        if not rarfile_ok:
            print("🔧 Installez rarfile: pip install rarfile")
        if not winrar_ok:
            print("🔧 Installez WinRAR depuis winrar.com")
        if not extraction_ok:
            print("🔧 Vérifiez le fichier RAR et le mot de passe")
    
    print(f"\n🎯 PROCHAINES ÉTAPES:")
    print("1. Lancer un nouveau téléchargement de Content Warning")
    print("2. Regarder attentivement la console du launcher")
    print("3. Chercher les messages d'extraction automatique")
    print("4. Si pas de messages → problème d'intégration")
    print("5. Si messages d'erreur → problème technique")
    
    return detection_ok and rarfile_ok and winrar_ok

if __name__ == "__main__":
    success = main()
    input(f"\n{'✅ Diagnostic terminé' if success else '❌ Problèmes détectés'} - Appuyez sur Entrée pour continuer...")
