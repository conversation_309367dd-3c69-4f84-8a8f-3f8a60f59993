#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour trouver WinRAR sur le système
"""

import os
import subprocess
from pathlib import Path

def find_winrar_installation():
    """Trouve l'installation de WinRAR sur Windows"""
    
    print("🔍 RECHERCHE DE WINRAR")
    print("=" * 50)
    
    # Chemins d'installation courants de WinRAR
    common_paths = [
        r"C:\Program Files\WinRAR\WinRAR.exe",
        r"C:\Program Files (x86)\WinRAR\WinRAR.exe",
        r"C:\Program Files\WinRAR\Rar.exe",
        r"C:\Program Files (x86)\WinRAR\Rar.exe",
        r"C:\WinRAR\WinRAR.exe",
        r"C:\WinRAR\Rar.exe"
    ]
    
    found_paths = []
    
    for path in common_paths:
        if os.path.exists(path):
            found_paths.append(path)
            print(f"✅ Trouvé: {path}")
    
    if not found_paths:
        print("❌ WinRAR non trouvé dans les emplacements courants")
        
        # Recherche dans le registre Windows
        try:
            import winreg
            print("\n🔍 Recherche dans le registre Windows...")
            
            # Clés de registre possibles
            registry_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WinRAR"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\WinRAR"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\WinRAR")
            ]
            
            for hkey, subkey in registry_keys:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        exe_path, _ = winreg.QueryValueEx(key, "exe32")
                        if os.path.exists(exe_path):
                            found_paths.append(exe_path)
                            print(f"✅ Trouvé via registre: {exe_path}")
                except:
                    continue
                    
        except ImportError:
            print("⚠️  Module winreg non disponible")
    
    return found_paths

def test_winrar_extraction():
    """Test l'extraction avec WinRAR trouvé"""
    
    print(f"\n🧪 TEST D'EXTRACTION AVEC WINRAR")
    print("=" * 50)
    
    winrar_paths = find_winrar_installation()
    
    if not winrar_paths:
        print("❌ Impossible de tester - WinRAR non trouvé")
        return False
    
    winrar_exe = winrar_paths[0]
    print(f"🔧 Utilisation de: {winrar_exe}")
    
    # Créer un fichier RAR de test
    import tempfile
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Créer un fichier de test
        test_file = os.path.join(temp_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("Test d'extraction WinRAR")
        
        # Créer une archive RAR avec mot de passe
        rar_file = os.path.join(temp_dir, "test.rar")
        
        try:
            # Commande pour créer l'archive
            cmd = [
                winrar_exe,
                "a",  # ajouter
                "-p" + "online-fix.me",  # mot de passe
                rar_file,
                test_file
            ]
            
            print("🔧 Création d'archive de test...")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Archive créée avec succès")
                
                # Tester l'extraction avec rarfile
                try:
                    import rarfile
                    
                    # Configurer rarfile pour utiliser WinRAR
                    rarfile.UNRAR_TOOL = winrar_exe
                    
                    extract_dir = os.path.join(temp_dir, "extracted")
                    os.makedirs(extract_dir)
                    
                    with rarfile.RarFile(rar_file) as rf:
                        rf.setpassword("online-fix.me")
                        rf.extractall(extract_dir)
                    
                    # Vérifier l'extraction
                    extracted_file = os.path.join(extract_dir, "test.txt")
                    if os.path.exists(extracted_file):
                        print("✅ Extraction réussie avec rarfile + WinRAR")
                        return winrar_exe
                    else:
                        print("❌ Fichier non extrait")
                        
                except Exception as e:
                    print(f"❌ Erreur extraction rarfile: {e}")
                    
            else:
                print(f"❌ Erreur création archive: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Erreur test WinRAR: {e}")
    
    return False

def update_rarfile_config():
    """Met à jour la configuration de rarfile pour utiliser WinRAR"""
    
    print(f"\n🔧 CONFIGURATION DE RARFILE")
    print("=" * 50)
    
    winrar_paths = find_winrar_installation()
    
    if not winrar_paths:
        print("❌ Impossible de configurer - WinRAR non trouvé")
        return None
    
    winrar_exe = winrar_paths[0]
    
    # Code à ajouter au launcher
    config_code = f'''
# Configuration rarfile pour WinRAR
import rarfile
rarfile.UNRAR_TOOL = r"{winrar_exe}"
'''
    
    print("Code à ajouter au début du launcher:")
    print(config_code)
    
    return winrar_exe

def main():
    """Fonction principale"""
    
    print("🚀 CRACKEN LAUNCHER - RECHERCHE WINRAR")
    print("=" * 70)
    print("Recherche et test de WinRAR pour l'extraction")
    print()
    
    # Rechercher WinRAR
    winrar_paths = find_winrar_installation()
    
    if winrar_paths:
        print(f"\n✅ WinRAR trouvé à {len(winrar_paths)} emplacement(s)")
        
        # Tester l'extraction
        winrar_exe = test_winrar_extraction()
        
        if winrar_exe:
            print(f"\n🎯 SOLUTION TROUVÉE:")
            print(f"WinRAR fonctionne: {winrar_exe}")
            
            # Générer le code de configuration
            update_rarfile_config()
            
            print(f"\n📝 PROCHAINES ÉTAPES:")
            print("1. Ajouter la configuration rarfile au launcher")
            print("2. Tester un nouveau téléchargement")
            print("3. Vérifier l'extraction automatique")
            
            return winrar_exe
        else:
            print(f"\n❌ WinRAR trouvé mais test d'extraction échoué")
    else:
        print(f"\n❌ WinRAR non trouvé sur le système")
        print("💡 Vérifiez l'installation de WinRAR")
    
    return None

if __name__ == "__main__":
    winrar_path = main()
    input(f"\n{'✅ WinRAR configuré' if winrar_path else '❌ Problème WinRAR'} - Appuyez sur Entrée pour continuer...")
