import tkinter as tk
from tkinter import filedialog, Frame, Canvas, Scrollbar, Menu, simpledialog, messagebox, Button
import subprocess
import json
import os
from PIL import Image, ImageTk, ImageDraw
import time
import sys
import shutil
import requests
import tempfile
import base64
from io import BytesIO
import datetime
# import pyrebase  # Temporairement désactivé pour test
import webbrowser
import http.server
import socketserver
import threading
from urllib.parse import urlparse, parse_qs
import aria2p
import hashlib
import secrets
import platform
import glob
import zipfile
import rarfile

# Configuration rarfile pour utiliser UnRAR (plus compatible)
import os
if os.path.exists(r"C:\Program Files\WinRAR\UnRAR.exe"):
    rarfile.UNRAR_TOOL = r"C:\Program Files\WinRAR\UnRAR.exe"
    print(f"✅ Configuration rarfile: UnRAR.exe trouvé")
elif os.path.exists(r"C:\Program Files\WinRAR\WinRAR.exe"):
    rarfile.UNRAR_TOOL = r"C:\Program Files\WinRAR\WinRAR.exe"
    print(f"⚠️  Configuration rarfile: Utilisation de WinRAR.exe (peut causer des problèmes)")
else:
    print(f"❌ Aucun outil RAR trouvé")

# --- CONSTANTES ---
if getattr(sys, 'frozen', False):
    # Si l'application est exécutée en tant qu'exécutable PyInstaller
    APPLICATION_PATH = sys._MEIPASS
else:
    # Si l'application est exécutée en tant que script Python
    APPLICATION_PATH = os.path.dirname(os.path.abspath(__file__))

# Configuration JSONBin.io
JSONBIN_API_KEY = "$2a$10$21w2X8AqLFHvfCHhQ7Rmn.q5ygWLFka8wbFgWVC2ADFQHM7cjBGtS"  # À remplacer par votre clé API
JSONBIN_BIN_ID = "684c56e88960c979a5a954f4"    # À remplacer par l'ID de votre bin
JSONBIN_URL = f"https://api.jsonbin.io/v3/b/{JSONBIN_BIN_ID}"

# Lancer aria2c.exe automatiquement au démarrage
ARIA2C_PATH = os.path.join(APPLICATION_PATH, "tools", "aria2c.exe")
aria2c_process = None

def start_aria2c():
    global aria2c_process
    if aria2c_process is None or aria2c_process.poll() is not None:
        try:
            creationflags = 0
            if sys.platform == "win32":
                creationflags = subprocess.CREATE_NO_WINDOW
            aria2c_process = subprocess.Popen([
                ARIA2C_PATH,
                "--enable-rpc",
                "--rpc-listen-all=true",
                "--rpc-allow-origin-all",
                "--quiet=true"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, creationflags=creationflags)
            time.sleep(1)  # Laisser le temps à aria2c de démarrer
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible de lancer aria2c.exe : {e}")
            return False
    return True

# Initialiser l'API aria2p
aria2 = None
if start_aria2c():
    try:
        aria2 = aria2p.API(aria2p.Client(host="http://localhost", port=6800, secret=""))
    except Exception as e:
        messagebox.showerror("Erreur", f"Impossible de se connecter à aria2c : {e}")

def download_image_from_url(url):
    """Télécharge une image depuis une URL et retourne un objet Image."""
    try:
        response = requests.get(url)
        response.raise_for_status()
        return Image.open(BytesIO(response.content))
    except Exception as e:
        print(f"Erreur lors du téléchargement de l'image : {e}")
        return None

def download_db():
    """Télécharge la base de données depuis JSONBin.io"""
    try:
        headers = {
            "X-Access-Key": JSONBIN_API_KEY
        }
        response = requests.get(JSONBIN_URL, headers=headers)
        response.raise_for_status()
        return response.json()['record']
    except Exception as e:
        print(f"Erreur lors du téléchargement de la base de données : {e}")
        return None

def get_resource_path(relative_path):
    """Obtient le chemin absolu vers une ressource, fonctionne pour le dev et pour PyInstaller"""
    try:
        # PyInstaller crée un dossier temporaire et stocke le chemin dans _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = APPLICATION_PATH
    return os.path.join(base_path, relative_path)

def set_window_icon(window):
    """Applique l'icône personnalisée à une fenêtre"""
    try:
        # Utiliser l'icône multi-résolution HD en priorité
        multi_hd_path = get_resource_path("exe_icon_multi_hd.ico")
        if os.path.exists(multi_hd_path):
            window.iconbitmap(multi_hd_path)
        else:
            # Fallback vers l'icône 256x256
            icon_256_path = get_resource_path("exe_icon_256.ico")
            if os.path.exists(icon_256_path):
                window.iconbitmap(icon_256_path)
            else:
                # Fallback vers l'icône 128x128
                icon_128_path = get_resource_path("exe_icon_128.ico")
                if os.path.exists(icon_128_path):
                    window.iconbitmap(icon_128_path)
    except Exception as e:
        print(f"Impossible d'appliquer l'icône à la fenêtre : {e}")

# Chemins des fichiers de données
USER_DATA_DIR = os.path.join(os.path.expanduser("~"), "Documents", "CrackenLauncher")
USER_GAMES_FILE = os.path.join(USER_DATA_DIR, 'launcher_data.json')
IMAGES_DIR = os.path.join(USER_DATA_DIR, 'images')  # Nouveau dossier pour les images
STORE_CACHE_DIR = os.path.join(USER_DATA_DIR, 'store_cache')  # Dossier de cache permanent pour le store

# Créer les dossiers de données utilisateur s'ils n'existent pas
if not os.path.exists(USER_DATA_DIR):
    os.makedirs(USER_DATA_DIR)
if not os.path.exists(IMAGES_DIR):
    os.makedirs(IMAGES_DIR)
if not os.path.exists(STORE_CACHE_DIR):
    os.makedirs(STORE_CACHE_DIR)

IMAGE_SIZE = (150, 200)
MIN_COLUMNS = 3
PADDING = 15
CLICK_DELAY = 3
DEFAULT_WINDOW_SIZE = "1280x800"

# --- FONCTIONS DE CHIFFREMENT ---
def get_device_key():
    """Génère une clé unique basée sur l'appareil pour le chiffrement"""
    # Utiliser des informations système pour créer une clé unique
    device_info = f"{platform.node()}-{platform.system()}-{platform.machine()}"
    return hashlib.sha256(device_info.encode()).digest()

def encrypt_token(token):
    """Chiffre le token avec une clé basée sur l'appareil"""
    if not token:
        return ""

    try:
        # Utiliser XOR simple avec la clé de l'appareil
        device_key = get_device_key()
        token_bytes = token.encode('utf-8')

        # Répéter la clé pour qu'elle soit au moins aussi longue que le token
        key_repeated = (device_key * ((len(token_bytes) // len(device_key)) + 1))[:len(token_bytes)]

        # XOR chaque byte
        encrypted_bytes = bytes(a ^ b for a, b in zip(token_bytes, key_repeated))

        # Encoder en base64 pour le stockage
        return base64.b64encode(encrypted_bytes).decode('utf-8')
    except Exception as e:
        print(f"❌ ENCRYPT ERROR: {e}")
        return ""

def decrypt_token(encrypted_token):
    """Déchiffre le token avec la clé de l'appareil"""
    if not encrypted_token:
        return ""

    try:
        # Décoder depuis base64
        encrypted_bytes = base64.b64decode(encrypted_token.encode('utf-8'))

        # Utiliser la même clé pour déchiffrer
        device_key = get_device_key()
        key_repeated = (device_key * ((len(encrypted_bytes) // len(device_key)) + 1))[:len(encrypted_bytes)]

        # XOR pour déchiffrer
        decrypted_bytes = bytes(a ^ b for a, b in zip(encrypted_bytes, key_repeated))

        return decrypted_bytes.decode('utf-8')
    except Exception as e:
        print(f"❌ DECRYPT ERROR: {e}")
        return ""

# --- VARIABLES GLOBALES ---
last_launch_time = 0
current_active_button = None # Variable globale pour suivre le bouton actif
stats_scroll_after_id = None  # ID du callback after pour la page stats
user_logged_in = False
user_logged_name = None
user_logged_email = None
current_user_token = None
current_login_window = None  # Référence à la fenêtre de connexion active
google_info_window = None  # Référence à la fenêtre d'information Google
current_settings_window = None  # Référence à la fenêtre de paramètres active
current_settings_category = "account"  # Catégorie active dans les paramètres
auth_polling_active = False  # Contrôle du polling d'authentification
library_sort_mode = "date_added"  # Mode de tri de la bibliothèque: "date_added", "name", "last_played"
download_tasks = []  # Liste globale des téléchargements en cours
downloads_refresh_after_id = None

# --- COULEURS ---
BACKGROUND_COLOR = "#0B1317"
BUTTON_COLOR = "#44C283"
HELP_BUTTON_COLOR = "#3498db" # Un bleu pour le bouton d'aide
TEXT_COLOR = "#FFFFFF"
GAME_BUTTON_BACKGROUND = "#206C6C"
MENU_BACKGROUND = "#2c3e50"
MENU_FOREGROUND = "#ecf0f1"
ACTIVE_BUTTON_COLOR = "#3A9F6B" # Un vert plus foncé pour l'état actif

# --- GESTION DES DONNÉES ---

# Variables globales pour la recherche
search_term = ""  # Recherche dans la bibliothèque
store_search_term = ""  # Recherche dans le store
cached_master_db = None  # Cache de la base de données du store
temp_images_folder = None  # Dossier temporaire pour les images
loading_window = None  # Fenêtre de chargement

def on_search_change(event=None):
    """Fonction appelée quand le texte de recherche change"""
    global search_term
    current_text = search_entry.get()

    # Ignorer le placeholder
    if current_text == "Rechercher un jeu...":
        search_term = ""
    else:
        search_term = current_text

    update_games_grid()

    # Mettre à jour le style du bouton clear selon le contenu
    if search_term.strip():
        clear_button.config(bg="#e74c3c", state="normal")
    else:
        clear_button.config(bg=HELP_BUTTON_COLOR, state="normal")

def clear_search():
    """Fonction pour effacer la recherche"""
    global search_term
    search_term = ""
    search_entry.delete(0, tk.END)
    search_entry.focus_set()  # Remettre le focus sur la barre de recherche
    clear_button.config(bg=HELP_BUTTON_COLOR)
    update_games_grid()

def on_search_focus_in(event=None):
    """Fonction appelée quand la barre de recherche reçoit le focus"""
    if search_entry.get() == "Rechercher un jeu...":
        search_entry.delete(0, tk.END)
        search_entry.config(fg=TEXT_COLOR)

def on_search_focus_out(event=None):
    """Fonction appelée quand la barre de recherche perd le focus"""
    if not search_entry.get().strip():
        search_entry.insert(0, "Rechercher un jeu...")
        search_entry.config(fg="#7f8c8d")

# Fonctions de recherche pour le Store
def on_store_search_change(event=None):
    """Fonction appelée quand le texte de recherche du store change"""
    global store_search_term
    current_text = store_search_entry.get()

    # Ignorer le placeholder
    if current_text == "Rechercher dans le store...":
        store_search_term = ""
    else:
        store_search_term = current_text

    update_store_games()

    # Mettre à jour le style du bouton clear selon le contenu
    if store_search_term.strip():
        store_clear_button.config(bg="#e74c3c", state="normal")
    else:
        store_clear_button.config(bg=HELP_BUTTON_COLOR, state="normal")

def clear_store_search():
    """Fonction pour effacer la recherche du store"""
    global store_search_term
    store_search_term = ""
    store_search_entry.delete(0, tk.END)
    store_search_entry.focus_set()  # Remettre le focus sur la barre de recherche
    store_clear_button.config(bg=HELP_BUTTON_COLOR)
    update_store_games()

def on_store_search_focus_in(event=None):
    """Fonction appelée quand la barre de recherche du store reçoit le focus"""
    if store_search_entry.get() == "Rechercher dans le store...":
        store_search_entry.delete(0, tk.END)
        store_search_entry.config(fg=TEXT_COLOR)

def on_store_search_focus_out(event=None):
    """Fonction appelée quand la barre de recherche du store perd le focus"""
    if not store_search_entry.get().strip():
        store_search_entry.insert(0, "Rechercher dans le store...")
        store_search_entry.config(fg="#7f8c8d")

def update_store_games():
    """Met à jour l'affichage des jeux du store selon le terme de recherche"""
    global store_search_term, current_store_container, cached_master_db

    # Vérifier si le conteneur existe
    if 'current_store_container' not in globals() or current_store_container is None:
        return

    # Effacer le contenu actuel
    for widget in current_store_container.winfo_children():
        widget.destroy()

    # Utiliser la base de données en cache
    if not cached_master_db:
        tk.Label(current_store_container, text="Aucun jeu disponible.",
                font=("Helvetica", 14), bg=BACKGROUND_COLOR, fg=TEXT_COLOR).pack(pady=40)
        return

    # Filtrer les jeux selon le terme de recherche
    if store_search_term.strip():
        filtered_games = {
            game_id: game_data for game_id, game_data in cached_master_db.items()
            if store_search_term.lower() in game_data.get('official_name', '').lower()
        }
    else:
        filtered_games = cached_master_db

    # Afficher les jeux filtrés
    display_store_games(current_store_container, filtered_games)

def display_store_games(container_frame, games_dict):
    """Affiche les jeux du store dans le conteneur donné"""
    print(f"🔍 DEBUG: display_store_games() appelée avec {len(games_dict)} jeu(s)")

    # Création du canvas sans scrollbar visible
    canvas = tk.Canvas(container_frame, bg=BACKGROUND_COLOR, highlightthickness=0)
    games_frame = tk.Frame(canvas, bg=BACKGROUND_COLOR)
    games_frame_id = canvas.create_window((0, 0), window=games_frame, anchor="n")  # Centré horizontalement

    # Configuration du canvas pour qu'il prenne tout l'espace
    canvas.pack(fill="both", expand=True)

    # Fonction pour centrer le contenu dans le canvas
    def center_content(event=None):
        canvas_width = canvas.winfo_width()
        games_frame_width = games_frame.winfo_reqwidth()
        if canvas_width > games_frame_width:
            x_offset = (canvas_width - games_frame_width) // 2
        else:
            x_offset = 0
        canvas.coords(games_frame_id, x_offset, 0)

    # Lier l'événement de redimensionnement
    canvas.bind("<Configure>", center_content)
    games_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))

    # Garder le scroll avec la molette mais sans barre visible
    canvas.bind_all("<MouseWheel>", lambda e: canvas.yview_scroll(int(-1*(e.delta/120)), "units"))

    # Affichage des jeux avec largeur fixe pour un meilleur centrage
    for game_id, game_data in games_dict.items():
        print(f"🔍 DEBUG: Création carte pour {game_data.get('official_name', 'Jeu inconnu')} (ID: {game_id})")
        print(f"🔍 DEBUG: Type du jeu: '{game_data.get('type', 'AUCUN TYPE')}'")

        already_added = any(g.get('game_id') == game_id for g in user_games_list)
        print(f"🔍 DEBUG: Jeu déjà ajouté: {already_added}")
        card = tk.Frame(games_frame, bg=GAME_BUTTON_BACKGROUND, bd=2, relief="ridge", width=1200, height=160)
        card.pack(pady=10, padx=20)
        card.pack_propagate(False)  # Maintenir la largeur et hauteur fixes

        # Image du jeu (utiliser le cache permanent si disponible)
        try:
            cache_image_path = game_data.get('cache_image_path')
            if cache_image_path and os.path.exists(cache_image_path):
                # Utiliser l'image en cache permanent
                img = Image.open(cache_image_path)
            else:
                # Fallback: télécharger l'image directement
                img = download_image_from_url(game_data['image_url'])

            img = img.resize((80, 110), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(img)
        except Exception:
            img = Image.new('RGB', (80, 110), color='grey')
            photo = ImageTk.PhotoImage(img)
        img_label = tk.Label(card, image=photo, bg=GAME_BUTTON_BACKGROUND)
        img_label.image = photo
        img_label.place(x=10, y=15)  # Position fixe pour l'image

        # Informations du jeu
        info_frame = tk.Frame(card, bg=GAME_BUTTON_BACKGROUND)
        info_frame.place(x=110, y=15, width=700, height=130)  # Position et taille fixes pour les infos
        name_label = tk.Label(info_frame, text=game_data.get('official_name', 'Jeu inconnu'),
                             font=("Helvetica", 14, "bold"), bg=GAME_BUTTON_BACKGROUND, fg=TEXT_COLOR)
        name_label.pack(anchor="w")

        # Type de jeu (nouvelle ligne)
        game_type = game_data.get('type', 'Type non spécifié')
        type_label = tk.Label(info_frame, text=f"Type: {game_type}",
                             font=("Helvetica", 9, "italic"), bg=GAME_BUTTON_BACKGROUND, fg=TEXT_COLOR)
        type_label.pack(anchor="w", pady=(2,0))

        desc = game_data.get('description', 'Aucune description.')
        try:
            desc = desc.encode('latin1').decode('utf-8')
        except Exception:
            pass
        desc_label = tk.Label(info_frame, text=desc, font=("Helvetica", 10),
                             bg=GAME_BUTTON_BACKGROUND, fg=TEXT_COLOR, wraplength=500, justify="left")
        desc_label.pack(anchor="w", pady=(3,0))

        # Boutons d'action
        if already_added:
            already_added_btn = tk.Button(card, text=" Le jeu est déjà dans la bibliothèque !",
                                        state="disabled", font=("Helvetica", 12, "bold"),
                                        bg="#888", fg="#fff", relief="flat", padx=20, pady=10)
            already_added_btn.place(x=830, y=57)  # Centré dans la carte plus grande
        else:
            # Bouton "Ajouter à ma bibliothèque"
            def make_add_callback(game_id=game_id, game_data=game_data):
                def add_to_library():
                    # [Logique d'ajout à la bibliothèque - sera ajoutée dans la prochaine partie]
                    add_game_to_library(game_id, game_data)
                return add_to_library

            add_btn = tk.Button(card, text="Ajouter à ma bibliothèque", command=make_add_callback(),
                               font=("Helvetica", 12, "bold"), bg=BUTTON_COLOR, fg=TEXT_COLOR,
                               relief="flat", padx=15, pady=5)
            add_btn.place(x=830, y=35)  # Ajusté pour la carte plus grande

            # Bouton "Télécharger"
            def download_callback(game_id=game_id, game_data=game_data):
                print(f"🎯 DEBUG: Bouton Télécharger cliqué pour {game_data.get('official_name', 'Jeu inconnu')}")
                print(f"🔍 DEBUG: game_id = {game_id}")
                print(f"🔍 DEBUG: game_data = {game_data}")
                start_torrent_download(game_id, game_data)
            download_btn = tk.Button(card, text="Télécharger", command=download_callback,
                                   font=("Helvetica", 12, "bold"), bg=HELP_BUTTON_COLOR, fg=TEXT_COLOR,
                                   relief="flat", padx=15, pady=5)
            download_btn.place(x=830, y=90)  # Ajusté pour la carte plus grande
            print(f"✅ DEBUG: Bouton Télécharger créé pour {game_data.get('official_name', 'Jeu inconnu')}")

    # Espacement en bas
    tk.Frame(games_frame, height=40, bg=BACKGROUND_COLOR).pack()

def add_game_to_library(game_id, game_data):
    """Ajoute un jeu du store à la bibliothèque"""
    # Demander le dossier où se trouve l'exécutable
    game_folder = filedialog.askdirectory(
        title=f"Sélectionnez le dossier contenant l'exécutable de {game_data.get('official_name', 'ce jeu')}"
    )
    if not game_folder:
        return  # L'utilisateur a annulé

    # Vérifier si le nom de l'exécutable est défini dans la base de données
    exe_name = game_data.get('exe_name')
    if not exe_name:
        messagebox.showerror("Erreur", "Le nom de l'exécutable n'est pas défini dans la base de données pour ce jeu.")
        return

    # Construire le chemin complet vers l'exécutable
    exe_path = os.path.join(game_folder, exe_name)

    # Vérifier si l'exécutable existe
    if not os.path.exists(exe_path):
        messagebox.showerror(
            "Exécutable introuvable",
            f"L'exécutable '{exe_name}' n'a pas été trouvé dans le dossier sélectionné.\n\n"
            f"Dossier sélectionné : {game_folder}\n"
            f"Exécutable recherché : {exe_name}"
        )
        return

    # Vérifier si le jeu n'est pas déjà dans la bibliothèque
    for existing_game in user_games_list:
        if os.path.normpath(existing_game['path']) == os.path.normpath(exe_path):
            messagebox.showinfo("Jeu déjà présent", "Ce jeu est déjà dans votre bibliothèque.")
            return

    # Télécharger et sauvegarder l'image localement
    local_image_path = save_image_locally(game_data['image_url'], game_id)
    if not local_image_path:
        messagebox.showerror("Erreur", "Impossible de télécharger l'image du jeu.")
        return

    # Créer l'entrée du jeu
    new_game = {
        "name": game_data.get('official_name', 'Jeu inconnu'),
        "path": exe_path,
        "image_path": local_image_path,
        "description": game_data.get('description', 'Aucune description.'),
        "type": game_data.get('type', 'Type non spécifié'),
        "playtime": 0,
        "game_id": game_id
    }

    # Ajouter le jeu à la bibliothèque
    user_games_list.append(new_game)
    save_user_games(user_games_list)
    messagebox.showinfo("Succès", f"Le jeu '{new_game['name']}' a été ajouté à votre bibliothèque !")
    show_store_page()  # Rafraîchir la page du store

# === FONCTIONS DE CHARGEMENT ET CACHE ===

def create_loading_screen():
    """Crée l'écran de chargement"""
    global loading_window

    # Créer une fenêtre indépendante pour le chargement
    loading_window = tk.Tk()
    loading_window.title("Cracken Launcher - Chargement")
    loading_window.geometry("500x300")
    loading_window.configure(bg=BACKGROUND_COLOR)
    loading_window.resizable(False, False)

    # Appliquer l'icône Clems Games
    set_window_icon(loading_window)

    # Centrer la fenêtre sur l'écran
    loading_window.eval('tk::PlaceWindow . center')

    # Logo/Titre
    title_label = tk.Label(loading_window, text="🎮 Cracken Launcher",
                          font=("Helvetica", 24, "bold"),
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(pady=(50, 20))

    # Message de chargement
    loading_label = tk.Label(loading_window, text="Initialisation en cours...",
                            font=("Helvetica", 12),
                            bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    loading_label.pack(pady=10)

    # Barre de progression
    progress_frame = tk.Frame(loading_window, bg=BACKGROUND_COLOR)
    progress_frame.pack(pady=20, padx=50, fill="x")

    progress_bg = tk.Frame(progress_frame, bg="#2c3e50", height=20)
    progress_bg.pack(fill="x")

    progress_bar = tk.Frame(progress_bg, bg=BUTTON_COLOR, height=20)
    progress_bar.place(x=0, y=0, width=0, height=20)

    # Pourcentage
    percent_label = tk.Label(loading_window, text="0%",
                            font=("Helvetica", 10),
                            bg=BACKGROUND_COLOR, fg="#7f8c8d")
    percent_label.pack(pady=5)

    loading_window.update()

    return loading_window, loading_label, progress_bar, progress_bg, percent_label

def update_loading_progress(loading_label, progress_bar, progress_bg, percent_label, progress, message):
    """Met à jour la barre de progression"""
    if loading_window and loading_window.winfo_exists():
        loading_label.config(text=message)

        # Calculer la largeur de la barre
        total_width = progress_bg.winfo_width()
        if total_width > 1:  # S'assurer que la largeur est valide
            bar_width = int((progress / 100) * total_width)
            progress_bar.place(width=bar_width)

        percent_label.config(text=f"{progress}%")
        loading_window.update()

def download_image_to_cache(url, game_id):
    """Télécharge une image dans le cache permanent si elle n'existe pas déjà
    Retourne (chemin_fichier, était_en_cache)"""
    global STORE_CACHE_DIR

    if not url or not game_id:
        return None, False

    # Vérifier d'abord si l'image existe déjà dans le cache
    for ext in ['.jpg', '.png', '.gif', '.jpeg']:
        cached_path = os.path.join(STORE_CACHE_DIR, f"{game_id}{ext}")
        if os.path.exists(cached_path):
            return cached_path, True

    # L'image n'existe pas en cache, la télécharger
    try:
        print(f"Téléchargement de l'image: {game_id}")
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            # Déterminer l'extension du fichier
            content_type = response.headers.get('content-type', '')
            if 'jpeg' in content_type or 'jpg' in content_type:
                ext = '.jpg'
            elif 'png' in content_type:
                ext = '.png'
            elif 'gif' in content_type:
                ext = '.gif'
            else:
                ext = '.jpg'  # Par défaut

            # Sauvegarder dans le cache permanent
            cache_path = os.path.join(STORE_CACHE_DIR, f"{game_id}{ext}")
            with open(cache_path, 'wb') as f:
                f.write(response.content)

            return cache_path, False
    except Exception as e:
        print(f"Erreur lors du téléchargement de l'image {url}: {e}")

    return None, False

def preload_store_data():
    """Pré-charge la base de données et les images du store avec cache intelligent"""
    global cached_master_db, loading_window

    try:
        # Créer l'écran de chargement
        loading_window, loading_label, progress_bar, progress_bg, percent_label = create_loading_screen()

        # Étape 1: Télécharger la base de données
        update_loading_progress(loading_label, progress_bar, progress_bg, percent_label, 10, "Téléchargement de la base de données...")

        cached_master_db = load_master_db()
        if not cached_master_db:
            update_loading_progress(loading_label, progress_bar, progress_bg, percent_label, 100, "Erreur lors du chargement de la base de données")
            return False

        # Étape 2: Vérifier et télécharger les images manquantes
        total_games = len(cached_master_db)
        current_game = 0
        images_downloaded = 0
        images_from_cache = 0

        for game_id, game_data in cached_master_db.items():
            current_game += 1
            progress = 10 + int((current_game / total_games) * 80)  # 10% à 90%

            update_loading_progress(loading_label, progress_bar, progress_bg, percent_label,
                                  progress, f"Vérification des images... ({current_game}/{total_games})")

            # Vérifier/télécharger l'image avec le cache intelligent
            image_url = game_data.get('image_url')
            if image_url:
                cache_path, was_cached = download_image_to_cache(image_url, game_id)
                if cache_path:
                    # Mettre à jour le chemin dans la base de données en cache
                    cached_master_db[game_id]['cache_image_path'] = cache_path
                    # Compter les statistiques
                    if was_cached:
                        images_from_cache += 1
                    else:
                        images_downloaded += 1

        # Finalisation avec statistiques
        cache_stats = f"Images: {images_from_cache} en cache, {images_downloaded} téléchargées"
        update_loading_progress(loading_label, progress_bar, progress_bg, percent_label, 100, f"Terminé ! {cache_stats}")

        # Afficher les statistiques dans la console
        print(f"=== STATISTIQUES DE CACHE ===")
        print(f"Images trouvées en cache: {images_from_cache}")
        print(f"Images téléchargées: {images_downloaded}")
        print(f"Total des jeux: {total_games}")

        # Attendre un peu pour que l'utilisateur voie les statistiques
        loading_window.after(2000, lambda: loading_window.destroy() if loading_window.winfo_exists() else None)
        loading_window.update()
        time.sleep(2.0)

        return True

    except Exception as e:
        print(f"Erreur lors du pré-chargement: {e}")
        if loading_window and loading_window.winfo_exists():
            loading_window.destroy()
        return False

def cleanup_temp_files():
    """Nettoie les anciens fichiers temporaires (garde le cache permanent)"""
    # Note: Le cache permanent dans STORE_CACHE_DIR est conservé
    # On nettoie seulement les anciens dossiers temporaires
    cleanup_old_temp_files()

def cleanup_old_temp_files():
    """Nettoie les anciens fichiers temporaires au démarrage"""
    try:
        temp_dir = tempfile.gettempdir()
        for item in os.listdir(temp_dir):
            if item.startswith("cracken_launcher_"):
                folder_path = os.path.join(temp_dir, item)
                if os.path.isdir(folder_path):
                    try:
                        shutil.rmtree(folder_path)
                        print(f"Ancien dossier temporaire supprimé: {item}")
                    except Exception as e:
                        print(f"Impossible de supprimer {item}: {e}")
    except Exception as e:
        print(f"Erreur lors du nettoyage des anciens fichiers temporaires: {e}")

def load_user_games():
    """Charge la liste des jeux de l'utilisateur et les préférences."""
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
            if isinstance(data, dict):
                return data.get('games', []), data.get('window_size', DEFAULT_WINDOW_SIZE)
            return data, DEFAULT_WINDOW_SIZE
    except (FileNotFoundError, json.JSONDecodeError):
        return [], DEFAULT_WINDOW_SIZE

def load_master_db():
    """Charge la base de données depuis JSONBin.io"""
    try:
        db = download_db()
        if db is None:
            messagebox.showerror("Erreur de connexion", "Impossible de se connecter à la base de données. Veuillez vérifier votre connexion internet.")
            return {}
        return db
    except Exception as e:
        print(f"Erreur lors du chargement de la base de données : {e}")
        messagebox.showerror("Erreur", f"Une erreur est survenue lors du chargement de la base de données : {e}")
        return {}

def save_user_games(games_list):
    """Sauvegarde la liste des jeux de l'utilisateur."""
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        data = {'games': []}

    data['games'] = games_list

    with open(USER_GAMES_FILE, 'w') as f:
        json.dump(data, f, indent=4)

def save_window_size():
    """Sauvegarde la taille de la fenêtre."""
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        data = {'games': []}

    data['window_size'] = f"{root.winfo_width()}x{root.winfo_height()}"

    with open(USER_GAMES_FILE, 'w') as f:
        json.dump(data, f, indent=4)

def on_closing():
    """Gère la fermeture de la fenêtre."""
    global download_tasks, aria2c_process

    # EXPLICATION: On vérifie si la liste des tâches contient des téléchargements qui ne sont PAS terminés/annulés/en erreur.
    active_downloads = [
        task for task in download_tasks
        if task.get('state') not in ("complete", "error", "removed", "Terminé", "Annulé", "Erreur")
    ]

    if active_downloads:
        # MODIFIÉ: On cache la fenêtre principale au lieu d'afficher une pop-up.
        # L'utilisateur verra toujours la fenêtre de progression du téléchargement.
        root.withdraw()
        # La messagebox a été supprimée pour une expérience plus fluide.
        return # Empêche la destruction de la fenêtre.

    # S'il n'y a pas de téléchargements actifs, on ferme normalement.
    save_window_size()

    # Nettoyer les fichiers temporaires
    cleanup_temp_files()

    if aria2c_process is not None:
        try:
            # Tente de mettre fin au processus aria2c
            aria2.remove_all(force=True) # S'assure que aria2 nettoie ses tâches
            aria2c_process.terminate()
            aria2c_process.wait(timeout=2) # Attendre jusqu'à 2 secondes
        except Exception as e:
            print(f"Erreur lors de la fermeture de aria2c : {e}")
            # Forcer la fermeture si la terminaison normale échoue
            if aria2c_process.poll() is None:
                aria2c_process.kill()

    root.destroy()

def load_user_last_login():
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
            return data.get('last_user', None)
    except Exception:
        return None

def save_user_last_login(username):
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
    except Exception:
        data = {'games': []}
    data['last_user'] = username
    with open(USER_GAMES_FILE, 'w') as f:
        json.dump(data, f, indent=4)

def save_sort_preference():
    """Sauvegarde la préférence de tri"""
    global library_sort_mode
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
    except Exception:
        data = {'games': []}

    data['sort_mode'] = library_sort_mode

    with open(USER_GAMES_FILE, 'w') as f:
        json.dump(data, f, indent=4)

def load_sort_preference():
    """Charge la préférence de tri"""
    global library_sort_mode
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
            library_sort_mode = data.get('sort_mode', 'date_added')
    except Exception:
        library_sort_mode = 'date_added'

# --- FONCTIONS PRINCIPALES ---

def show_help_dialog():
    """Affiche la boîte de dialogue d'aide."""
    help_text = "Pour ajouter un jeu au launcher, vous devez placer le fichier LAUNCHER.cg (qui se trouve sur le cloud du site, au même emplacement que le torrent du jeu), dans le même dossier que celui contenant le fichier .exe du jeu correspondant."
    messagebox.showinfo(
        "Aide - Comment ajouter un jeu ?",
        help_text
    )

def save_image_locally(image_url, game_id):
    """Télécharge et sauvegarde une image localement."""
    try:
        response = requests.get(image_url)
        response.raise_for_status()

        # Créer un nom de fichier unique basé sur l'ID du jeu
        image_path = os.path.join(IMAGES_DIR, f"{game_id}.png")

        # Sauvegarder l'image
        with open(image_path, 'wb') as f:
            f.write(response.content)

        return image_path
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de l'image : {e}")
        return None

def get_local_image_path(game_id):
    """Retourne le chemin de l'image locale si elle existe."""
    image_path = os.path.join(IMAGES_DIR, f"{game_id}.png")
    return image_path if os.path.exists(image_path) else None

def launch_game(executable_path, button=None):
    global last_launch_time
    if not executable_path: return

    current_time = time.time()
    if current_time - last_launch_time < CLICK_DELAY:
        return

    # Effet visuel de clic
    if button:
        original_bg = button.cget('bg')
        button.configure(bg='#1A4A4A')  # Version plus foncée de GAME_BUTTON_BACKGROUND
        root.update()
        root.after(100, lambda: button.configure(bg=original_bg))

    # Trouver le jeu correspondant
    game_index = None
    for i, game in enumerate(user_games_list):
        if os.path.normpath(game['path']) == os.path.normpath(executable_path):
            game_index = i
            break

    if game_index is None:
        print(f"Jeu non trouvé pour le chemin : {executable_path}")
        return

    try:
        # Obtenir le répertoire de l'exécutable pour définir le répertoire de travail
        executable_dir = os.path.dirname(executable_path)

        # Essayer d'abord avec le répertoire de travail défini
        try:
            process = subprocess.Popen([executable_path], cwd=executable_dir)
        except Exception as e:
            print(f"Erreur avec cwd, essai sans : {e}")
            # Si cela échoue, essayer la méthode originale
            process = subprocess.Popen([executable_path])

        last_launch_time = current_time
        root.withdraw()  # Cache complètement la fenêtre au lieu de la réduire

        # Mettre à jour le timestamp de dernière utilisation
        user_games_list[game_index]['last_use'] = current_time
        save_user_games(user_games_list)

        # Fonction pour vérifier si le processus est toujours en cours
        def check_process():
            if process.poll() is None:  # Le processus est toujours en cours
                root.after(1000, check_process)  # Vérifier à nouveau dans 1 seconde
            else:  # Le processus est terminé
                # Calculer le temps de jeu
                end_time = time.time()
                playtime = end_time - last_launch_time
                # Mettre à jour le temps de jeu
                user_games_list[game_index]['playtime'] = user_games_list[game_index].get('playtime', 0) + playtime
                save_user_games(user_games_list)
                root.deiconify()  # Restaure le launcher
                root.state('normal')  # Assure que la fenêtre est dans un état normal
                root.focus_force()  # Force le focus sur la fenêtre

                # Recalculer la taille de la fenêtre après un court délai
                root.after(100, check_window_size)

        # Démarrer la surveillance du processus
        check_process()

    except Exception as e:
        print(f"Erreur lancement: {e}")

def set_active_button(button_to_activate): # Nouvelle fonction pour gérer l'état actif des boutons de navigation
    global current_active_button
    # Réinitialiser le bouton précédemment actif
    if current_active_button and current_active_button != button_to_activate:
        current_active_button.configure(relief="flat", bg=BUTTON_COLOR)

    # Définir le nouveau bouton comme actif
    button_to_activate.configure(relief="sunken", bg=ACTIVE_BUTTON_COLOR)
    current_active_button = button_to_activate

def modify_game_name(game_index):
    game = user_games_list[game_index]

    # Création d'une fenêtre de dialogue personnalisée
    dialog = tk.Toplevel(root)
    dialog.title("Modifier le nom")
    dialog.geometry("400x200")
    dialog.configure(bg=BACKGROUND_COLOR)
    set_window_icon(dialog)  # Appliquer l'icône personnalisée
    dialog.transient(root)  # Rend la fenêtre dépendante de la fenêtre principale
    dialog.grab_set()  # Rend la fenêtre modale

    # Centrer la fenêtre
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    x = (dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (dialog.winfo_screenheight() // 2) - (height // 2)
    dialog.geometry(f'{width}x{height}+{x}+{y}')

    # Message
    message = tk.Label(dialog, text="Nouveau nom :",
                      bg=BACKGROUND_COLOR, fg=TEXT_COLOR,
                      font=("Helvetica", 10))
    message.pack(pady=10)

    # Champ de saisie
    entry = tk.Entry(dialog, font=("Helvetica", 10))
    entry.insert(0, game['name'])
    entry.pack(pady=10, padx=20, fill=tk.X)
    entry.select_range(0, tk.END)
    entry.focus_set()

    # Frame pour les boutons
    button_frame = tk.Frame(dialog, bg=BACKGROUND_COLOR)
    button_frame.pack(pady=20)

    def on_confirm():
        new_name = entry.get().strip()
        if new_name:  # Vérifie que le nom n'est pas vide
            game['name'] = new_name
            save_user_games(user_games_list)
            update_games_grid()
        dialog.destroy()

    def on_cancel():
        dialog.destroy()

    # Boutons
    confirm_btn = tk.Button(button_frame, text="Confirmer", command=on_confirm,
                          bg=BUTTON_COLOR, fg=TEXT_COLOR,
                          font=("Helvetica", 10), padx=20)
    cancel_btn = tk.Button(button_frame, text="Annuler", command=on_cancel,
                         bg=HELP_BUTTON_COLOR, fg=TEXT_COLOR,
                         font=("Helvetica", 10), padx=20)

    confirm_btn.pack(side=tk.LEFT, padx=10)
    cancel_btn.pack(side=tk.LEFT, padx=10)

    # Gérer la fermeture de la fenêtre
    dialog.protocol("WM_DELETE_WINDOW", on_cancel)

    # Gérer la touche Entrée
    entry.bind('<Return>', lambda e: on_confirm())
    entry.bind('<Escape>', lambda e: on_cancel())

def modify_game_image(game_index):
    game = user_games_list[game_index]
    new_image_path = filedialog.askopenfilename(title=f"Nouvelle image pour {game['name']}", filetypes=[("Images", "*.png *.jpg")])
    if new_image_path:
        # Copier la nouvelle image dans le dossier des images
        game_id = game.get('game_id', str(time.time()))  # Utiliser l'ID existant ou en créer un nouveau
        target_path = os.path.join(IMAGES_DIR, f"{game_id}.png")

        try:
            # Copier le fichier
            shutil.copy2(new_image_path, target_path)
            game['image_path'] = target_path
            save_user_games(user_games_list)
            update_games_grid()
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible de sauvegarder la nouvelle image : {e}")

def delete_game(game_index):
    game_name = user_games_list[game_index]['name']

    # Création d'une fenêtre de dialogue personnalisée
    dialog = tk.Toplevel(root)
    dialog.title("Confirmation")
    dialog.geometry("300x150")
    dialog.configure(bg=BACKGROUND_COLOR)
    set_window_icon(dialog)  # Appliquer l'icône personnalisée
    dialog.transient(root)  # Rend la fenêtre dépendante de la fenêtre principale
    dialog.grab_set()  # Rend la fenêtre modale

    # Centrer la fenêtre
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    x = (dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (dialog.winfo_screenheight() // 2) - (height // 2)
    dialog.geometry(f'{width}x{height}+{x}+{y}')

    # Message
    message = tk.Label(dialog, text=f"Supprimer '{game_name}' ?",
                      bg=BACKGROUND_COLOR, fg=TEXT_COLOR,
                      font=("Helvetica", 10))
    message.pack(pady=20)

    # Frame pour les boutons
    button_frame = tk.Frame(dialog, bg=BACKGROUND_COLOR)
    button_frame.pack(pady=10)

    def on_confirm():
        user_games_list.pop(game_index)
        save_user_games(user_games_list)
        update_games_grid()
        dialog.destroy()

    def on_cancel():
        dialog.destroy()

    # Boutons
    confirm_btn = tk.Button(button_frame, text="Oui", command=on_confirm,
                          bg=BUTTON_COLOR, fg=TEXT_COLOR,
                          font=("Helvetica", 10), padx=20)
    cancel_btn = tk.Button(button_frame, text="Non", command=on_cancel,
                         bg=HELP_BUTTON_COLOR, fg=TEXT_COLOR,
                         font=("Helvetica", 10), padx=20)

    confirm_btn.pack(side=tk.LEFT, padx=10)
    cancel_btn.pack(side=tk.LEFT, padx=10)

    # Gérer la fermeture de la fenêtre
    dialog.protocol("WM_DELETE_WINDOW", on_cancel)

def show_context_menu(event, game_index):
    # Réinitialiser le menu
    context_menu.delete(0, "end")
    modify_menu.delete(0, "end")

    # Recréer le sous-menu Modifier
    modify_menu.add_command(label="Changer le nom", command=lambda: modify_game_name(game_index))
    modify_menu.add_command(label="Changer l'image", command=lambda: modify_game_image(game_index))

    # Recréer le menu principal
    context_menu.add_command(label="Lancer", command=lambda: launch_game(user_games_list[game_index]['path']))
    context_menu.add_separator()
    context_menu.add_cascade(label="Modifier", menu=modify_menu)
    context_menu.add_command(label="Supprimer", command=lambda: delete_game(game_index))
    context_menu.add_separator()
    context_menu.add_command(label="Annuler")

    context_menu.tk_popup(event.x_root, event.y_root)



def show_game_page(game_index):
    # Masquer la grille et la barre de recherche
    main_frame.place_forget()
    search_frame.place_forget()
    for widget in game_page_frame.winfo_children():
        widget.destroy()

    # Récupérer les infos du jeu
    game = user_games_list[game_index]
    description = game.get('description', 'Aucune description disponible.')
    # Décoder les caractères spéciaux
    try:
        description = description.encode('latin1').decode('utf-8')
    except:
        pass  # Si le décodage échoue, on garde la description telle quelle

    # Frame principale qui prend tout l'espace
    menu_height = top_frame.winfo_height()
    game_page_frame.place(x=0, y=menu_height, relwidth=1, relheight=1)
    game_page_frame.pack_propagate(False)

    # Frame centrale pour le contenu, centrée verticalement et horizontalement
    content_frame = tk.Frame(game_page_frame, bg=BACKGROUND_COLOR)
    content_frame.place(relx=0.5, rely=0.5, anchor="center")

    # Layout horizontal : image à gauche, description et bouton à droite
    left_frame = tk.Frame(content_frame, bg=BACKGROUND_COLOR)
    left_frame.pack(side="left", padx=(0,60), pady=10)
    try:
        # Utiliser l'image locale
        img = Image.open(game["image_path"])
        img = img.resize((300, 400), Image.Resampling.LANCZOS)
        photo = ImageTk.PhotoImage(img)
    except Exception as e:
        print(f"Erreur chargement image : {e}")
        img = Image.new('RGB', (300, 400), color='grey')
        photo = ImageTk.PhotoImage(img)
    img_label = tk.Label(left_frame, image=photo, bg=BACKGROUND_COLOR)
    img_label.image = photo
    img_label.pack()

    right_frame = tk.Frame(content_frame, bg=BACKGROUND_COLOR)
    right_frame.pack(side="left", pady=10)
    name_label = tk.Label(right_frame, text=game['name'], font=("Helvetica", 18, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    name_label.pack(anchor="center", pady=(10, 20))

    # Ajouter le type de jeu
    game_type = game.get('type', 'Type non spécifié')
    type_label = tk.Label(right_frame, text=f"Type: {game_type}",
                         font=("Helvetica", 11, "italic"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    type_label.pack(anchor="center", pady=(0, 10))

    # Ajouter le temps de jeu
    playtime = game.get('playtime', 0)
    hours = int(playtime // 3600)
    minutes = int((playtime % 3600) // 60)
    playtime_label = tk.Label(right_frame, text=f"Temps de jeu : {hours}h {minutes}m",
                            font=("Helvetica", 12), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    playtime_label.pack(anchor="center", pady=(0, 20))

    desc_label = tk.Label(right_frame, text=description, wraplength=600, justify="left", font=("Helvetica", 12), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    desc_label.pack(anchor="center", pady=(0, 30))
    launch_btn = tk.Button(right_frame, text="Lancer le jeu", command=lambda: launch_game(game['path']), font=("Helvetica", 14, "bold"), bg=BUTTON_COLOR, fg=TEXT_COLOR, relief="flat", padx=30, pady=10)
    launch_btn.pack(anchor="center")

    # Bouton retour en haut à gauche
    retour_btn = tk.Button(game_page_frame, text="⬅ Retour", command=lambda: show_games_grid(), font=("Helvetica", 12, "bold"), bg=BUTTON_COLOR, fg=TEXT_COLOR, relief="flat", padx=15, pady=5)
    retour_btn.place(x=20, y=20)

    # Afficher la page de jeu directement
    menu_height = top_frame.winfo_height()
    game_page_frame.place(x=0, y=menu_height, relwidth=1, relheight=1)

def get_current_visible_frame():
    if main_frame.winfo_ismapped():
        return main_frame
    if game_page_frame.winfo_ismapped():
        return game_page_frame
    if stats_page_frame.winfo_ismapped():
        return stats_page_frame
    if store_page_frame.winfo_ismapped():
        return store_page_frame
    if downloads_page_frame.winfo_ismapped():
        return downloads_page_frame
    return main_frame

def show_games_grid():
    global stats_scroll_after_id, downloads_refresh_after_id
    if downloads_refresh_after_id:
        root.after_cancel(downloads_refresh_after_id)
        downloads_refresh_after_id = None

    # Masquer toutes les autres frames
    downloads_page_frame.place_forget()
    stats_page_frame.place_forget()
    store_page_frame.place_forget()
    game_page_frame.place_forget()

    if stats_scroll_after_id:
        root.after_cancel(stats_scroll_after_id)
        stats_scroll_after_id = None

    update_games_grid()
    set_active_button(library_button)

    # Afficher directement la bibliothèque
    menu_height = top_frame.winfo_height()
    search_height = 50
    # Afficher la barre de recherche
    search_frame.place(x=0, y=menu_height, relwidth=1, height=50)
    main_frame.place(x=0, y=menu_height + search_height, relwidth=1, relheight=1, height=-search_height)

def show_stats_page():
    global stats_scroll_after_id, downloads_refresh_after_id
    if downloads_refresh_after_id:
        root.after_cancel(downloads_refresh_after_id)
        downloads_refresh_after_id = None
    downloads_page_frame.place_forget()
    for widget in stats_page_frame.winfo_children():
        widget.destroy()
    title_label = tk.Label(stats_page_frame, text="Statistiques de jeu",
                          font=("Helvetica", 24, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(pady=20)
    games_frame = tk.Frame(stats_page_frame, bg=BACKGROUND_COLOR)
    games_frame.pack(fill="both", expand=True, padx=20)
    sorted_games = sorted(user_games_list, key=lambda x: x.get('playtime', 0), reverse=True)
    canvas = tk.Canvas(games_frame, bg=BACKGROUND_COLOR, highlightthickness=0)
    scrollbar = tk.Scrollbar(games_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg=BACKGROUND_COLOR)
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    for i, game in enumerate(sorted_games):
        playtime = game.get('playtime', 0)
        hours = int(playtime // 3600)
        minutes = int((playtime % 3600) // 60)
        game_frame = tk.Frame(scrollable_frame, bg=BACKGROUND_COLOR)
        game_frame.pack(fill="x", pady=5)
        name_label = tk.Label(game_frame, text=game['name'], font=("Helvetica", 12, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
        name_label.pack(side="left", padx=10)
        time_label = tk.Label(game_frame, text=f"{hours}h {minutes}m", font=("Helvetica", 12), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
        time_label.pack(side="right", padx=10)
    canvas.pack(side="left", fill="both", expand=True)
    def check_scrollbar():
        try:
            if scrollable_frame.winfo_exists() and canvas.winfo_exists():
                if scrollable_frame.winfo_height() > canvas.winfo_height():
                    scrollbar.pack(side="right", fill="y")
                else:
                    scrollbar.pack_forget()
                global stats_scroll_after_id
                stats_scroll_after_id = root.after(100, check_scrollbar)
        except tk.TclError:
            pass  # Widget détruit, on ne fait rien
    check_scrollbar()

    # Afficher directement la page des stats
    menu_height = top_frame.winfo_height()
    stats_page_frame.place(x=0, y=menu_height, relwidth=1, relheight=1)

def update_games_grid():
    for widget in grid_frame.winfo_children():
        widget.destroy()

    # Filtrer les jeux selon le terme de recherche
    global search_term, library_sort_mode
    if search_term.strip():
        filtered_games = [game for game in user_games_list if search_term.lower() in game['name'].lower()]
    else:
        filtered_games = user_games_list.copy()

    # Appliquer le tri selon la préférence
    if library_sort_mode == "name":
        filtered_games.sort(key=lambda x: x['name'].lower())
    elif library_sort_mode == "last_played":
        filtered_games.sort(key=lambda x: x.get('last_use', 0), reverse=True)
    # Pour "date_added", on garde l'ordre original (pas de tri)

    # Calculer le nombre de colonnes en fonction de la largeur de la fenêtre
    canvas_width = canvas.winfo_width()
    if canvas_width > 0:  # Éviter la division par zéro
        available_width = canvas_width - PADDING * 2
        columns = max(MIN_COLUMNS, available_width // (IMAGE_SIZE[0] + PADDING * 2))
    else:
        columns = MIN_COLUMNS

    for i, game in enumerate(filtered_games):
        # Trouver l'index original du jeu dans user_games_list
        original_index = user_games_list.index(game)

        row, col = i // columns, i % columns
        try:
            # Utiliser l'image locale
            img = Image.open(game["image_path"])
            img = img.resize(IMAGE_SIZE, Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(img)
        except Exception as e:
            print(f"Erreur chargement image : {e}")
            img = Image.new('RGB', IMAGE_SIZE, color='grey')
            photo = ImageTk.PhotoImage(img)

        btn = tk.Button(grid_frame, image=photo, text=game['name'],
                       font=("Helvetica", 9), compound="top",
                       bg=GAME_BUTTON_BACKGROUND, fg=TEXT_COLOR,
                       relief="raised", borderwidth=1, highlightthickness=0)
        btn.image = photo

        # Variables pour suivre l'état du clic
        btn.click_start_time = 0
        btn.click_start_pos = None

        def on_press(event, button=btn):
            button.click_start_time = time.time()
            button.click_start_pos = (event.x, event.y)

        def on_release(event, index=original_index, button=btn):
            # Vérifier si le clic a été maintenu trop longtemps ou déplacé
            if (time.time() - button.click_start_time > 0.5 or  # Plus de 0.5 secondes
                button.click_start_pos and  # Si on a une position de départ
                (abs(event.x - button.click_start_pos[0]) > 5 or  # Déplacé horizontalement
                 abs(event.y - button.click_start_pos[1]) > 5)):  # Déplacé verticalement
                return
            show_game_page(index)

        btn.bind("<Button-1>", on_press)
        btn.bind("<ButtonRelease-1>", on_release)
        btn.bind("<Button-3>", lambda e, index=original_index: show_context_menu(e, index))
        btn.grid(row=row, column=col, padx=PADDING, pady=PADDING)

    # Mettre à jour la région de défilement
    grid_frame.update_idletasks()
    canvas.configure(scrollregion=canvas.bbox("all"))

    # Afficher/masquer la barre de défilement selon le besoin
    if grid_frame.winfo_height() > canvas.winfo_height():
        scrollbar.pack(side="right", fill="y")
    else:
        scrollbar.pack_forget()

def on_mousewheel(event):
    # Empêcher le scroll vers le haut si déjà en haut, ou vers le bas si déjà en bas
    first, last = canvas.yview()
    if event.delta > 0 and first <= 0:
        return  # Déjà tout en haut
    if event.delta < 0 and last >= 1:
        return  # Déjà tout en bas
    canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

def on_canvas_configure(event):
    update_games_grid()

# --- INTERFACE ---
root = tk.Tk()
root.title("Cracken Launcher")
try:
    # Utiliser l'icône multi-résolution HD en priorité (jusqu'à 256x256)
    multi_hd_path = get_resource_path("exe_icon_multi_hd.ico")
    if os.path.exists(multi_hd_path):
        root.iconbitmap(multi_hd_path)
    else:
        # Fallback vers l'icône 256x256
        icon_256_path = get_resource_path("exe_icon_256.ico")
        if os.path.exists(icon_256_path):
            root.iconbitmap(icon_256_path)
            print("✓ Icône 256x256 haute définition chargée avec succès!")
        else:
            # Fallback vers l'icône 128x128
            icon_128_path = get_resource_path("exe_icon_128.ico")
            if os.path.exists(icon_128_path):
                root.iconbitmap(icon_128_path)
                print("✓ Icône 128x128 chargée avec succès!")
            else:
                # Dernier fallback vers l'ancienne icône
                old_icon_path = get_resource_path("exe_icon.ico")
                if os.path.exists(old_icon_path):
                    root.iconbitmap(old_icon_path)
                    print("✓ Ancienne icône chargée")
                else:
                    print("✗ Aucune icône trouvée")
except tk.TclError as e:
    print(f"✗ Erreur TclError lors du chargement de l'icône : {e}")
except Exception as e:
    print(f"✗ Erreur générale lors du chargement de l'icône : {e}")

# Charger les jeux et la taille de la fenêtre
user_games_list, window_size = load_user_games()
root.geometry(window_size)



def check_window_size():
    current_width = root.winfo_width()
    current_height = root.winfo_height()
    target_width = 1920
    target_height = 1009

    if current_width == target_width and current_height == target_height:
        root.state('zoomed')  # Maximise la fenêtre

# Attendre que la fenêtre soit complètement initialisée
root.after(100, check_window_size)

root.config(bg=BACKGROUND_COLOR)
context_menu = Menu(root, tearoff=0, bg=MENU_BACKGROUND, fg=MENU_FOREGROUND, activebackground=BUTTON_COLOR, activeforeground=TEXT_COLOR)
modify_menu = Menu(context_menu, tearoff=0, bg=MENU_BACKGROUND, fg=MENU_FOREGROUND, activebackground=BUTTON_COLOR, activeforeground=TEXT_COLOR)
modify_menu.add_command(label="Changer le nom"); modify_menu.add_command(label="Changer l'image")
context_menu.add_cascade(label="Modifier", menu=modify_menu); context_menu.add_command(label="Supprimer"); context_menu.add_separator(); context_menu.add_command(label="Annuler")
top_frame = Frame(root, bg=BACKGROUND_COLOR); top_frame.pack(fill="x", pady=10)
root.update()  # Force l'affichage du menu avant de calculer menu_height

# Ajout du logo ClemsLauncher en haut à gauche
try:
    logo_path = get_resource_path("crackenlauncher_logo.png")
    logo_img = Image.open(logo_path)
    logo_img = logo_img.resize((175, 75), Image.Resampling.LANCZOS)
    logo_photo = ImageTk.PhotoImage(logo_img)
    logo_label = tk.Label(top_frame, image=logo_photo, bg=BACKGROUND_COLOR)
    logo_label.image = logo_photo  # Garde une référence
    logo_label.pack(side="left", padx=20)
except Exception as e:
    print(f"Erreur chargement du logo : {e}")

# Boutons "Bibliothèque", "Store" et "Stats" à gauche
library_button = tk.Button(top_frame, text="📚 Bibliothèque", command=show_games_grid, font=("Segoe UI Emoji", 14, "bold"), bg=BUTTON_COLOR, fg=TEXT_COLOR, relief="flat", padx=15, pady=5)
library_button.pack(side="left", padx=(5, 5)) # Ajustement du padding

store_button = tk.Button(top_frame, text="🛒 Store", font=("Segoe UI Emoji", 14, "bold"), bg=BUTTON_COLOR, fg=TEXT_COLOR, relief="flat", padx=15, pady=5)
store_button.pack(side="left", padx=(5, 0))

# stats_button = tk.Button(top_frame, text="📊 Stats", command=show_stats_page, font=("Segoe UI Emoji", 14, "bold"), bg=BUTTON_COLOR, fg=TEXT_COLOR, relief="flat", padx=15, pady=5) # Command mis à jour
# stats_button.pack(side="left", padx=(5, 0)) # Ajustement du padding

root.update()  # Force l'affichage du menu avant de calculer menu_height

# Frame pour la barre de recherche
search_frame = tk.Frame(root, bg=BACKGROUND_COLOR, height=50)
menu_height = top_frame.winfo_height()
search_frame.place(x=0, y=menu_height, relwidth=1, height=50)

# Barre de recherche
search_label = tk.Label(search_frame, text="🔍", font=("Segoe UI Emoji", 16), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
search_label.pack(side="left", padx=(20, 5), pady=10)

search_entry = tk.Entry(search_frame, font=("Helvetica", 12), bg="#2c3e50", fg="#7f8c8d",
                       insertbackground=TEXT_COLOR, relief="flat", bd=5)
search_entry.pack(side="left", padx=(0, 10), pady=10, fill="x", expand=True)
search_entry.insert(0, "Rechercher un jeu...")
search_entry.bind('<KeyRelease>', on_search_change)
search_entry.bind('<FocusIn>', on_search_focus_in)
search_entry.bind('<FocusOut>', on_search_focus_out)

clear_button = tk.Button(search_frame, text="✖", font=("Helvetica", 10), bg=HELP_BUTTON_COLOR,
                        fg=TEXT_COLOR, relief="flat", padx=10, command=clear_search, cursor="hand2")
clear_button.pack(side="right", padx=(0, 20), pady=10)

main_frame = Frame(root, bg=BACKGROUND_COLOR)
search_height = 50
main_frame.place(x=0, y=menu_height + search_height, relwidth=1, relheight=1, height=-search_height)

canvas = Canvas(main_frame, bg=BACKGROUND_COLOR, highlightthickness=0)
scrollbar = Scrollbar(main_frame, orient="vertical", command=canvas.yview)
canvas.configure(yscrollcommand=scrollbar.set)

scrollbar.pack(side="right", fill="y")
canvas.pack(side="left", fill="both", expand=True)

grid_frame = Frame(canvas, bg=BACKGROUND_COLOR)
canvas.create_window((0, 0), window=grid_frame, anchor="nw")

# Nouvelle frame pour la page de jeu
game_page_frame = Frame(root, bg=BACKGROUND_COLOR)

# Nouvelle frame pour la page de statistiques
stats_page_frame = Frame(root, bg=BACKGROUND_COLOR)

# Nouvelle frame pour la page du store
store_page_frame = Frame(root, bg=BACKGROUND_COLOR)

# Nouvelle frame pour la page de téléchargements
downloads_page_frame = Frame(root, bg=BACKGROUND_COLOR)

# Lier les événements
canvas.bind("<Configure>", on_canvas_configure)
canvas.bind_all("<MouseWheel>", on_mousewheel)  # Pour Windows
canvas.bind_all("<Button-4>", lambda e: canvas.yview_scroll(-1, "units"))  # Pour Linux
canvas.bind_all("<Button-5>", lambda e: canvas.yview_scroll(1, "units"))  # Pour Linux

# Configurer la gestion de la fermeture de la fenêtre
root.protocol("WM_DELETE_WINDOW", on_closing)

# Charger les préférences de tri avant d'afficher la grille
load_sort_preference()

user_games_list = user_games_list  # Réassigner la liste des jeux
update_games_grid()
set_active_button(library_button) # Définir le bouton Bibliothèque comme actif au démarrage

# Configuration Firebase
firebaseConfig = {
    "apiKey": "AIzaSyD0Dlx2H_HTLIBPnjyWiIqTsYxSQNzutUg",
    "authDomain": "cracken-auth.firebaseapp.com",
    "databaseURL": "https://cracken-auth-default-rtdb.europe-west1.firebasedatabase.app/",
    "projectId": "cracken-auth",
    "storageBucket": "cracken-auth.firebasestorage.app",
    "messagingSenderId": "************",
    "appId": "1:************:web:fdc71a275be566b3db993e"
}

# Initialisation Firebase
firebase = None
auth = None
db = None

try:
    # Essayer d'abord avec pyrebase4 (version plus récente)
    try:
        import pyrebase4 as pyrebase  # type: ignore
        firebase = pyrebase.initialize_app(firebaseConfig)
        auth = firebase.auth()
        db = firebase.database()
    except ImportError:
        # Fallback vers pyrebase classique
        import pyrebase  # type: ignore
        firebase = pyrebase.initialize_app(firebaseConfig)
        auth = firebase.auth()
        db = firebase.database()

except ImportError:
    print("Erreur: Aucune version de pyrebase n'est installée.")
    print("Veuillez installer pyrebase4 avec: pip install pyrebase4")
except Exception as e:
    print(f"Erreur lors de l'initialisation Firebase: {e}")

# --- FONCTIONS D'AUTHENTIFICATION ---

def load_user_auth_data():
    """Charge les données d'authentification - Mode 2: Local chiffré + Firebase"""
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)

        user_session = data.get('user_session', {})
        if not user_session or not user_session.get('auto_login', False):
            return {}

        email = user_session.get('email', '')
        encrypted_token = user_session.get('encrypted_token', '')

        if not email or not encrypted_token:
            return {}

        # Déchiffrer le token
        decrypted_token = decrypt_token(encrypted_token)
        if not decrypted_token:
            print("❌ DECRYPT: Impossible de déchiffrer le token")
            return {}

        return {
            'email': email,
            'token': decrypted_token,
            'last_login': user_session.get('last_login', '')
        }

    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"📁 LOCAL: Aucune session locale trouvée: {e}")
        return {}

def save_user_auth_data(email, display_name, token, nickname=None):
    """Sauvegarde les données d'authentification - Mode 2: Local chiffré + Firebase"""

    # 1. Sauvegarder sur Firebase (pseudo et métadonnées)
    firebase_success = False
    if db:
        try:
            user_key = email.replace('.', '_').replace('@', '_at_')

            user_data = {
                'email': email,
                'display_name': display_name,
                'nickname': nickname,
                'created_at': str(datetime.datetime.now()),
                'last_updated': str(datetime.datetime.now()),
                'last_seen': str(datetime.datetime.now()),
                'auth_method': 'google_oauth',
                'device_info': {
                    'name': platform.node(),
                    'os': platform.system(),
                    'last_login': str(datetime.datetime.now())
                }
            }

            db.child("users").child(user_key).set(user_data)
            print(f"✅ FIREBASE: Données utilisateur sauvegardées pour {email}")
            firebase_success = True

        except Exception as e:
            print(f"❌ FIREBASE ERROR: Erreur sauvegarde Firebase: {e}")

    # 2. Sauvegarder localement (token chiffré + email pour auto-login)
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        data = {'games': []}

    # Chiffrer le token avant sauvegarde
    encrypted_token = encrypt_token(token)

    data['user_session'] = {
        'email': email,
        'encrypted_token': encrypted_token,
        'last_login': str(datetime.datetime.now()),
        'auto_login': True
    }

    with open(USER_GAMES_FILE, 'w') as f:
        json.dump(data, f, indent=4)

    print(f"🔐 LOCAL: Token chiffré sauvegardé localement pour {email}")
    return True

def clear_user_auth_data():
    """Supprime les données d'authentification locales - Mode 2"""
    try:
        with open(USER_GAMES_FILE, 'r') as f:
            data = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        data = {'games': []}

    # Supprimer uniquement la session locale
    if 'user_session' in data:
        del data['user_session']
        print("🔄 LOGOUT: Session locale supprimée")

    with open(USER_GAMES_FILE, 'w') as f:
        json.dump(data, f, indent=4)

    print("🔐 LOGOUT: Données locales supprimées (Firebase conservé)")
    return True

# --- FONCTIONS DE GESTION DU PSEUDO ---

def save_user_nickname_to_firebase(email, nickname):
    """Sauvegarde le pseudo de l'utilisateur UNIQUEMENT sur Firebase"""
    if not db:
        print("❌ FIREBASE REQUIRED: Firebase requis pour sauvegarder le pseudo")
        messagebox.showerror("Erreur",
            "Firebase est requis pour sauvegarder votre pseudo.\n"
            "Vérifiez votre connexion internet et réessayez.")
        return False

    def firebase_save_operation():
        # Utiliser l'email comme clé unique (en remplaçant les caractères spéciaux)
        user_key = email.replace('.', '_').replace('@', '_at_')

        user_data = {
            'email': email,
            'nickname': nickname,
            'created_at': str(datetime.datetime.now()),
            'last_updated': str(datetime.datetime.now()),
            'storage_type': 'firebase_only'
        }

        db.child("users").child(user_key).set(user_data)
        return True

    # Utiliser le système de retry
    firebase_success = firebase_retry_operation(firebase_save_operation, max_retries=3, delay=1)

    if not firebase_success:
        print(f"❌ FIREBASE ERROR: Impossible de sauvegarder le pseudo après plusieurs tentatives")
        messagebox.showerror("Erreur de synchronisation",
            f"Impossible de sauvegarder le pseudo sur Firebase.\n"
            f"Vérifiez votre connexion internet et réessayez.\n\n"
            f"Le pseudo ne sera pas sauvegardé.")
        return False

    print(f"🔥 FIREBASE ONLY: Pseudo '{nickname}' sauvegardé uniquement sur Firebase")
    return True

def get_user_nickname_from_firebase(email):
    """Récupère le pseudo de l'utilisateur UNIQUEMENT depuis Firebase"""
    if not db:
        print("❌ FIREBASE REQUIRED: Firebase requis pour récupérer le pseudo")
        return None

    firebase_nickname = None

    def firebase_get_operation():
        nonlocal firebase_nickname
        # Utiliser l'email comme clé unique (en remplaçant les caractères spéciaux)
        user_key = email.replace('.', '_').replace('@', '_at_')

        user_data = db.child("users").child(user_key).get()
        if user_data.val():
            firebase_nickname = user_data.val().get('nickname')
            return True
        else:
            print(f"🔍 FIREBASE: Aucun pseudo trouvé sur Firebase pour {email}")
            return False

    # Utiliser le système de retry
    success = firebase_retry_operation(firebase_get_operation, max_retries=2, delay=0.5)

    if success and firebase_nickname:
        return firebase_nickname
    elif not success:
        print(f"❌ FIREBASE ERROR: Impossible de récupérer le pseudo après plusieurs tentatives")

    print(f"❌ NO NICKNAME: Aucun pseudo trouvé sur Firebase pour {email}")
    return None

def sync_local_nicknames_to_firebase():
    """Plus de synchronisation locale - Système 100% Firebase"""
    print("🔥 FIREBASE ONLY: Plus de synchronisation locale nécessaire")
    print("✅ Tous les pseudos sont maintenant gérés uniquement sur Firebase")
    return

def force_sync_current_user_to_firebase():
    """Force la synchronisation du pseudo de l'utilisateur actuel vers Firebase"""
    global user_logged_email, user_logged_name

    if not user_logged_email or not user_logged_name:
        print("⚠️  FORCE SYNC: Aucun utilisateur connecté à synchroniser")
        return False

    if not db:
        print("⚠️  FORCE SYNC: Firebase non disponible")
        return False

    try:
        print(f"🔄 FORCE SYNC: Synchronisation forcée du pseudo '{user_logged_name}' pour {user_logged_email}")

        user_key = user_logged_email.replace('.', '_').replace('@', '_at_')
        user_data = {
            'email': user_logged_email,
            'nickname': user_logged_name,
            'created_at': str(datetime.datetime.now()),
            'last_updated': str(datetime.datetime.now()),
            'force_synced': True
        }

        db.child("users").child(user_key).set(user_data)

        # Mettre à jour les données locales
        save_user_auth_data(user_logged_email, user_logged_name, current_user_token, user_logged_name)

        print(f"✅ FORCE SYNC: Pseudo '{user_logged_name}' synchronisé avec succès")
        return True

    except Exception as e:
        print(f"❌ FORCE SYNC ERROR: Échec de la synchronisation forcée: {e}")
        return False

def firebase_retry_operation(operation_func, max_retries=3, delay=1):
    """
    Exécute une opération Firebase avec retry automatique

    Args:
        operation_func: Fonction à exécuter (doit retourner True en cas de succès)
        max_retries: Nombre maximum de tentatives
        delay: Délai en secondes entre les tentatives

    Returns:
        bool: True si l'opération a réussi, False sinon
    """
    import time

    for attempt in range(max_retries):
        try:
            result = operation_func()
            if result:
                if attempt > 0:
                    print(f"🔄 RETRY SUCCESS: Opération réussie après {attempt + 1} tentative(s)")
                return True
        except Exception as e:
            print(f"❌ RETRY ATTEMPT {attempt + 1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                print(f"⏳ RETRY: Nouvelle tentative dans {delay} seconde(s)...")
                time.sleep(delay)
                delay *= 2  # Backoff exponentiel
            else:
                print(f"❌ RETRY FAILED: Toutes les tentatives ont échoué")

    return False

def show_nickname_dialog(email):
    """Affiche la fenêtre de saisie du pseudo pour la première connexion - Requiert Firebase"""
    # Vérifier que Firebase est disponible
    if not db:
        messagebox.showerror("Firebase requis",
            "Firebase est requis pour créer un pseudo.\n"
            "Vérifiez votre connexion internet et relancez le launcher.")
        return None

    nickname_window = tk.Toplevel(root)
    nickname_window.title("Choisir un pseudo")
    nickname_window.geometry("450x350")
    nickname_window.configure(bg=BACKGROUND_COLOR)
    set_window_icon(nickname_window)
    nickname_window.transient(root)
    nickname_window.grab_set()

    # Empêcher la fermeture de la fenêtre sans validation
    def on_close_nickname():
        # Ne rien faire si l'utilisateur essaie de fermer la fenêtre
        pass

    nickname_window.protocol("WM_DELETE_WINDOW", on_close_nickname)

    # Centrer la fenêtre
    nickname_window.update_idletasks()
    width = nickname_window.winfo_width()
    height = nickname_window.winfo_height()
    x = (nickname_window.winfo_screenwidth() // 2) - (width // 2)
    y = (nickname_window.winfo_screenheight() // 2) - (height // 2)
    nickname_window.geometry(f'{width}x{height}+{x}+{y}')

    # Variable pour stocker le pseudo
    nickname_result = {"value": None}

    # Titre
    title_label = tk.Label(nickname_window, text="Première connexion",
                          font=("Helvetica", 18, "bold"),
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(pady=20)

    # Message d'explication
    info_text = "Choisissez un pseudo qui sera affiché dans le launcher."
    info_label = tk.Label(nickname_window, text=info_text,
                         font=("Helvetica", 11),
                         bg=BACKGROUND_COLOR, fg=TEXT_COLOR,
                         justify="center")
    info_label.pack(pady=10)

    # Info Firebase
    firebase_info = tk.Label(nickname_window,
                           text="🔥 Sauvegarde 100% Firebase - Synchronisé sur tous vos appareils",
                           font=("Helvetica", 10, "bold"),
                           bg=BACKGROUND_COLOR, fg="#e67e22")
    firebase_info.pack(pady=5)

    # Champ de saisie du pseudo
    nickname_frame = tk.Frame(nickname_window, bg=BACKGROUND_COLOR)
    nickname_frame.pack(pady=20)

    tk.Label(nickname_frame, text="Pseudo :",
             font=("Helvetica", 12, "bold"),
             bg=BACKGROUND_COLOR, fg=TEXT_COLOR).pack(anchor="w")

    nickname_entry = tk.Entry(nickname_frame, font=("Helvetica", 12),
                             width=30, bg="#2c3e50", fg=TEXT_COLOR,
                             insertbackground=TEXT_COLOR)
    nickname_entry.pack(pady=(5, 0))
    nickname_entry.focus()

    def validate_and_save():
        nickname = nickname_entry.get().strip()

        # Validation du pseudo
        if not nickname:
            error_label.config(text="Veuillez saisir un pseudo")
            return

        if len(nickname) < 2:
            error_label.config(text="Le pseudo doit contenir au moins 2 caractères")
            return

        if len(nickname) > 20:
            error_label.config(text="Le pseudo ne peut pas dépasser 20 caractères")
            return

        # Caractères interdits
        forbidden_chars = ['<', '>', '"', "'", '&', '\\', '/', '|']
        if any(char in nickname for char in forbidden_chars):
            error_label.config(text="Le pseudo contient des caractères interdits")
            return

        # Sauvegarder le pseudo
        if save_user_nickname_to_firebase(email, nickname):
            nickname_result["value"] = nickname
            nickname_window.destroy()
        else:
            error_label.config(text="Erreur lors de la sauvegarde. Réessayez.")
            return

    # Bouton de validation (placé avant le label d'erreur pour être plus visible)
    validate_btn = tk.Button(nickname_window, text="Confirmer",
                            command=validate_and_save,
                            font=("Helvetica", 14, "bold"),
                            bg="#27ae60", fg="white",
                            relief="flat", padx=40, pady=12,
                            cursor="hand2",
                            activebackground="#229954", activeforeground="white")
    validate_btn.pack(pady=15)

    # Label d'erreur (maintenant en dessous du bouton)
    error_label = tk.Label(nickname_window, text="",
                          font=("Helvetica", 10),
                          bg=BACKGROUND_COLOR, fg="#e74c3c")
    error_label.pack(pady=5)

    # Permettre la validation avec Entrée
    nickname_entry.bind('<Return>', lambda e: validate_and_save())

    # Attendre que la fenêtre soit fermée
    nickname_window.wait_window()

    return nickname_result["value"]



def login_with_google():
    """Connexion avec Google via OAuth2 avec serveur Vercel"""
    global auth_polling_active
    try:
        import urllib.parse
        import secrets
        import hashlib
        import base64
        import requests
        import time
        import threading

        # Générer un state unique comme session_id et code_verifier pour PKCE
        session_id = secrets.token_urlsafe(32)
        code_verifier = secrets.token_urlsafe(32)
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode()).digest()
        ).decode().rstrip('=')

        # Configuration OAuth2 Google
        client_id = "************-6dg1dfhsklmtbssqtt96vpqb7hq88196.apps.googleusercontent.com"
        client_secret = "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"
        redirect_uri = "https://cracken-auth.vercel.app/callback"
        scope = "openid email profile"
        vercel_server = "https://cracken-auth.vercel.app"

        # URL d'autorisation Google
        auth_url = (
            f"https://accounts.google.com/o/oauth2/v2/auth?"
            f"client_id={client_id}&"
            f"redirect_uri={urllib.parse.quote(redirect_uri)}&"
            f"scope={urllib.parse.quote(scope)}&"
            f"response_type=code&"
            f"state={session_id}&"
            f"code_challenge={code_challenge}&"
            f"code_challenge_method=S256&"
            f"prompt=select_account"
        )

        # Ouvrir le navigateur pour l'authentification Google
        print(f"Ouverture du navigateur pour l'authentification Google...")
        print(f"URL d'autorisation: {auth_url}")
        webbrowser.open(auth_url)

        # Afficher une fenêtre d'information
        # show_google_auth_info()  # Désactivé pour éviter l'affichage de la fenêtre

        # Démarrer le polling pour récupérer le résultat
        # Variables pour le polling
        polling_attempt = 0
        max_polling_attempts = 60  # 5 minutes maximum

        def poll_for_auth_result():
            """Fonction qui vérifie périodiquement si l'authentification est terminée"""
            global auth_polling_active
            nonlocal polling_attempt

            print(f"DEBUT poll_for_auth_result - attempt: {polling_attempt}, active: {auth_polling_active}")

            if not auth_polling_active or polling_attempt >= max_polling_attempts:
                print(f"ARRET polling - active: {auth_polling_active}, attempt: {polling_attempt}/{max_polling_attempts}")
                if polling_attempt >= max_polling_attempts:
                    handle_auth_error("Timeout - L'authentification a pris trop de temps")
                return

            polling_attempt += 1

            try:
                # Interroger le serveur Vercel
                poll_url = f"https://cracken-auth.vercel.app/callback?action=poll&session_id={session_id}"
                print(f"Polling tentative {polling_attempt}/{max_polling_attempts}: {poll_url}")
                response = requests.get(poll_url, timeout=10)

                print(f"Réponse serveur: {response.status_code} - {response.text}")

                if response.status_code == 200:
                    # Résultat reçu
                    result = response.json()
                    print(f"Résultat reçu: {result}")
                    if result.get('success'):
                        # Succès - traiter le code d'autorisation
                        print("Authentification réussie, traitement du code...")
                        handle_auth_success(result['code'], code_verifier, client_id, client_secret, redirect_uri)
                    else:
                        # Erreur
                        print(f"Erreur d'authentification: {result.get('error')}")
                        handle_auth_error(result.get('error', 'Erreur inconnue'))
                    auth_polling_active = False
                    return
                elif response.status_code == 202:
                    # Toujours en attente - programmer la prochaine tentative
                    print("Toujours en attente, nouvelle tentative dans 5 secondes...")
                    root.after(5000, poll_for_auth_result)  # 5 secondes
                else:
                    print(f"Erreur polling: {response.status_code}")
                    handle_auth_error(f"Erreur serveur: {response.status_code}")
                    auth_polling_active = False

            except requests.RequestException as e:
                print(f"Erreur réseau lors du polling: {e}")
                # Réessayer dans 5 secondes
                root.after(5000, poll_for_auth_result)
            except Exception as e:
                print(f"Erreur polling: {e}")
                handle_auth_error(f"Erreur polling: {str(e)}")
                auth_polling_active = False

        # Démarrer le polling avec Tkinter after (dans le thread principal)
        auth_polling_active = True

        def start_polling():
            print(f"Démarrage du polling pour session_id: {session_id}")
            try:
                poll_for_auth_result()
            except Exception as e:
                print(f"ERREUR dans start_polling: {e}")
                import traceback
                traceback.print_exc()

        # Démarrer le polling après 2 secondes pour laisser le temps à Google de rediriger
        print("Programmation du polling dans 2 secondes...")
        root.after(2000, start_polling)

    except Exception as e:
        print(f"Erreur lors de la connexion Google : {e}")
        messagebox.showerror("Erreur", f"Erreur lors de la connexion Google : {str(e)}")

def handle_auth_success(code, code_verifier, client_id, client_secret, redirect_uri):
    """Traiter le succès de l'authentification Google"""
    try:
        # Échanger le code d'autorisation contre un token d'accès
        token_url = "https://oauth2.googleapis.com/token"
        token_data = {
            'client_id': client_id,
            'client_secret': client_secret,
            'code': code,
            'code_verifier': code_verifier,
            'grant_type': 'authorization_code',
            'redirect_uri': redirect_uri
        }

        print(f"Échange du code pour token...")
        token_response = requests.post(token_url, data=token_data)
        print(f"Réponse token: {token_response.status_code}")

        if token_response.status_code == 200:
            token_info = token_response.json()
            print(f"Token reçu: {list(token_info.keys())}")

            if 'access_token' in token_info:
                # Obtenir les informations utilisateur
                user_info_url = f"https://www.googleapis.com/oauth2/v2/userinfo?access_token={token_info['access_token']}"
                user_response = requests.get(user_info_url)

                if user_response.status_code == 200:
                    user_data = user_response.json()
                    print(f"Données utilisateur: {user_data.get('email', 'N/A')}")
                    handle_google_login_success(user_data, token_info)
                else:
                    handle_auth_error("Impossible d'obtenir les informations utilisateur")
            else:
                handle_auth_error("Token d'accès non reçu")
        else:
            error_msg = token_response.text
            print(f"Erreur token: {error_msg}")
            handle_auth_error(f"Erreur OAuth: {error_msg}")

    except Exception as e:
        print(f"Erreur échange token : {e}")
        handle_auth_error(f"Erreur lors de l'échange du token : {str(e)}")

def handle_google_login_success(user_data, token_info):
    """Traiter le succès de la connexion Google"""
    try:
        global user_logged_in, user_logged_name, user_logged_email, current_user_token
        global current_login_window, google_info_window

        # Utiliser les données Google pour la connexion
        user_logged_in = True
        user_logged_email = user_data.get('email', '')
        user_logged_name = user_data.get('name', user_data.get('email', '').split('@')[0])
        current_user_token = token_info.get('access_token', '')

        # Fermer les fenêtres de connexion dès maintenant
        print(f"Fermeture des fenêtres - login_window: {current_login_window is not None}, google_window: {google_info_window is not None}")
        if current_login_window:
            print("Fermeture de la fenêtre de connexion...")
            current_login_window.destroy()
            current_login_window = None
        if google_info_window:
            print("Fermeture de la fenêtre d'authentification Google...")
            google_info_window.destroy()
            google_info_window = None

        # Vérifier si l'utilisateur a déjà un pseudo sur Firebase
        existing_nickname = get_user_nickname_from_firebase(user_logged_email)

        if existing_nickname:
            # L'utilisateur a déjà un pseudo, l'utiliser
            user_nickname = existing_nickname
            print(f"Pseudo existant trouvé: {user_nickname}")
        else:
            # Première connexion, demander un pseudo
            print("Première connexion détectée, demande de pseudo...")
            user_nickname = show_nickname_dialog(user_logged_email)

            if not user_nickname:
                # L'utilisateur a annulé ou une erreur s'est produite
                print("Échec de la saisie du pseudo - annulation de la connexion")
                # Réinitialiser l'état de connexion
                user_logged_in = False
                user_logged_name = None
                user_logged_email = None
                current_user_token = None
                # Mettre à jour l'interface pour refléter la déconnexion
                root.after(100, update_auth_ui)
                return

        # Mettre à jour le nom d'utilisateur avec le pseudo
        user_logged_name = user_nickname

        # Sauvegarder les données d'authentification avec le pseudo
        save_user_auth_data(user_logged_email, user_logged_name, current_user_token, user_nickname)

        # Lancer la synchronisation automatique après la connexion
        try:
            print("🔄 LOGIN-SYNC: Démarrage de la synchronisation après connexion...")
            sync_local_nicknames_to_firebase()
        except Exception as e:
            print(f"❌ LOGIN-SYNC ERROR: Erreur lors de la synchronisation après connexion: {e}")

        # Mettre à jour l'interface dans le thread principal
        def update_after_google_login():
            update_auth_ui()

        root.after(100, update_after_google_login)
        print("Connexion Google réussie!")

    except Exception as e:
        print(f"Erreur traitement connexion Google : {e}")
        handle_auth_error(f"Erreur traitement : {str(e)}")

def handle_auth_error(error_msg):
    """Traiter les erreurs d'authentification"""
    print(f"Erreur d'authentification: {error_msg}")
    messagebox.showerror("Erreur d'authentification", error_msg)

def show_google_auth_info():
    """Afficher une fenêtre d'information pendant l'authentification Google"""
    global google_info_window, auth_polling_active

    if google_info_window:
        google_info_window.destroy()

    google_info_window = tk.Toplevel(root)
    google_info_window.title("Authentification Google en cours...")
    google_info_window.geometry("400x200")
    google_info_window.resizable(False, False)

    # Centrer la fenêtre
    google_info_window.transient(root)
    google_info_window.grab_set()

    # Contenu de la fenêtre
    tk.Label(google_info_window, text="🔐 Authentification Google",
             font=("Arial", 14, "bold")).pack(pady=20)

    tk.Label(google_info_window, text="Veuillez vous connecter dans votre navigateur.",
             font=("Arial", 10)).pack(pady=5)

    tk.Label(google_info_window, text="Cette fenêtre se fermera automatiquement\naprès la connexion.",
             font=("Arial", 9), fg="gray").pack(pady=10)

    # Bouton pour annuler
    def cancel_auth():
        global google_info_window, auth_polling_active
        if google_info_window:
            google_info_window.destroy()
            google_info_window = None
        # Arrêter le polling
        auth_polling_active = False

    tk.Button(google_info_window, text="Annuler", command=cancel_auth,
              bg="#ff4444", fg="white", font=("Arial", 10)).pack(pady=20)

def check_auto_login():
    """Connexion automatique - Mode 2: Token local chiffré + pseudo Firebase"""
    global user_logged_in, user_logged_name, user_logged_email, current_user_token

    auth_data = load_user_auth_data()
    if auth_data and 'email' in auth_data and 'token' in auth_data:

        # Récupérer les données de session
        user_logged_email = auth_data.get('email', '')
        current_user_token = auth_data.get('token', '')

        # Récupérer le pseudo depuis Firebase
        if db:
            firebase_nickname = get_user_nickname_from_firebase(user_logged_email)
            if firebase_nickname:
                user_logged_name = firebase_nickname
                user_logged_in = True

                # Mettre à jour la dernière connexion sur Firebase
                try:
                    user_key = user_logged_email.replace('.', '_').replace('@', '_at_')
                    update_data = {
                        'last_seen': str(datetime.datetime.now()),
                        'device_info/last_login': str(datetime.datetime.now())
                    }
                    db.child("users").child(user_key).update(update_data)
                except Exception as e:
                    print(f"⚠️  FIREBASE: Erreur mise à jour dernière connexion: {e}")

                return True
            else:
                print("❌ AUTO-LOGIN: Impossible de récupérer le pseudo depuis Firebase")
                # Token valide mais pas de pseudo → demander création
                user_logged_in = True  # Connecté mais sans pseudo
                return True
        else:
            print("❌ AUTO-LOGIN: Firebase non disponible pour récupérer le pseudo")
            return False

    print("📭 AUTO-LOGIN: Aucune session locale trouvée")
    user_logged_in = False
    user_logged_name = ""
    user_logged_email = ""
    current_user_token = ""

    return False

def logout_user():
    """Déconnecte l'utilisateur - Mode 2: Supprime uniquement les données locales"""
    global user_logged_in, user_logged_name, user_logged_email, current_user_token

    # Sauvegarder l'email pour le message
    current_email = user_logged_email

    # Réinitialiser les variables globales
    user_logged_in = False
    user_logged_name = None
    user_logged_email = None
    current_user_token = None

    # Supprimer uniquement la session locale (pas Firebase)
    clear_user_auth_data()
    update_auth_ui()

    # Retourner à la bibliothèque après déconnexion
    show_games_grid()

    # Message informatif
    message = "🔐 Déconnexion locale réussie.\n\n"
    message += "• Session locale supprimée\n"
    message += "• Données Firebase conservées\n"
    message += "• Reconnexion automatique possible\n\n"
    message += "Vous devrez vous reconnecter pour accéder au store."

    messagebox.showinfo("Déconnexion", message)

# --- INTERFACES D'AUTHENTIFICATION ---

def show_google_info_window():
    """Affiche la fenêtre d'information pour la connexion Google"""
    global google_info_window

    google_info_window = tk.Toplevel(root)
    google_info_window.title("Connexion Google")
    google_info_window.geometry("400x200")
    google_info_window.configure(bg=BACKGROUND_COLOR)
    set_window_icon(google_info_window)
    google_info_window.transient(root)
    google_info_window.grab_set()

    # Centrer la fenêtre
    google_info_window.update_idletasks()
    width = google_info_window.winfo_width()
    height = google_info_window.winfo_height()
    x = (google_info_window.winfo_screenwidth() // 2) - (width // 2)
    y = (google_info_window.winfo_screenheight() // 2) - (height // 2)
    google_info_window.geometry(f'{width}x{height}+{x}+{y}')

    # Icône d'information
    info_label = tk.Label(google_info_window, text="ℹ️",
                         font=("Helvetica", 24),
                         bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    info_label.pack(pady=10)

    # Message
    message_label = tk.Label(google_info_window,
                           text="Votre navigateur va s'ouvrir pour la connexion Google.\nSuivez les instructions puis revenez au launcher.",
                           font=("Helvetica", 12),
                           bg=BACKGROUND_COLOR, fg=TEXT_COLOR,
                           justify="center")
    message_label.pack(pady=20)

    # Bouton OK
    ok_btn = tk.Button(google_info_window, text="OK",
                      command=lambda: close_google_info_window(),
                      font=("Helvetica", 12, "bold"),
                      bg=BUTTON_COLOR, fg=TEXT_COLOR,
                      relief="flat", padx=20, pady=8)
    ok_btn.pack(pady=10)

    def close_google_info_window():
        global google_info_window
        if google_info_window:
            google_info_window.destroy()
            google_info_window = None

    # Gérer la fermeture
    google_info_window.protocol("WM_DELETE_WINDOW", close_google_info_window)

def show_login_dialog():
    """Affiche la fenêtre de connexion Google uniquement"""
    global current_login_window
    login_window = tk.Toplevel(root)
    current_login_window = login_window  # Sauvegarder la référence
    login_window.title("Connexion")
    login_window.geometry("350x200")
    login_window.configure(bg=BACKGROUND_COLOR)
    set_window_icon(login_window)
    login_window.transient(root)
    login_window.grab_set()

    # Centrer la fenêtre
    login_window.update_idletasks()
    width = login_window.winfo_width()
    height = login_window.winfo_height()
    x = (login_window.winfo_screenwidth() // 2) - (width // 2)
    y = (login_window.winfo_screenheight() // 2) - (height // 2)
    login_window.geometry(f'{width}x{height}+{x}+{y}')

    # Titre
    title_label = tk.Label(login_window, text="Connexion",
                          font=("Helvetica", 18, "bold"),
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(pady=30)





    # Bouton Se connecter avec Google (centré et plus grand)
    google_btn = tk.Button(login_window, text="Se connecter avec Google", command=login_with_google,
                          font=("Helvetica", 14, "bold"), bg="#db4437", fg=TEXT_COLOR,
                          relief="flat", padx=30, pady=12)
    google_btn.pack(pady=20)

    def on_close_login():
        global current_login_window
        current_login_window = None
        login_window.destroy()

    login_window.protocol("WM_DELETE_WINDOW", on_close_login)



def show_settings_window():
    """Affiche la fenêtre des paramètres avec catégories à gauche"""
    global current_settings_window, current_settings_category

    # Vérifier si une fenêtre de paramètres est déjà ouverte
    if 'current_settings_window' in globals() and current_settings_window and current_settings_window.winfo_exists():
        current_settings_window.lift()  # Mettre la fenêtre au premier plan
        current_settings_window.focus_force()
        return

    # Créer la fenêtre de paramètres
    current_settings_window = tk.Toplevel(root)
    current_settings_window.title("Paramètres - Cracken Launcher")
    current_settings_window.geometry("700x500")
    current_settings_window.configure(bg=BACKGROUND_COLOR)
    current_settings_window.resizable(False, False)
    set_window_icon(current_settings_window)

    # Centrer la fenêtre
    current_settings_window.update_idletasks()
    width = current_settings_window.winfo_width()
    height = current_settings_window.winfo_height()
    x = (current_settings_window.winfo_screenwidth() // 2) - (width // 2)
    y = (current_settings_window.winfo_screenheight() // 2) - (height // 2)
    current_settings_window.geometry(f'{width}x{height}+{x}+{y}')

    # Titre
    title_label = tk.Label(current_settings_window, text="⚙️ Paramètres",
                          font=("Helvetica", 18, "bold"),
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(pady=(15, 20))

    # Frame principal pour le contenu
    main_content_frame = tk.Frame(current_settings_window, bg=BACKGROUND_COLOR)
    main_content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    # Frame de gauche pour les catégories avec bordure
    categories_border_frame = tk.Frame(main_content_frame, bg="#34495e", width=202, height=400)  # Frame pour la bordure
    categories_border_frame.pack(side="left", fill="y", padx=(0, 20))  # Espace de 20px entre menu et contenu
    categories_border_frame.pack_propagate(False)

    # Frame intérieur pour les catégories (même couleur que le fond)
    categories_frame = tk.Frame(categories_border_frame, bg=BACKGROUND_COLOR, width=200)
    categories_frame.pack(fill="both", expand=True, padx=1, pady=1)  # Padding de 1px pour créer la bordure
    categories_frame.pack_propagate(False)

    # Frame de droite pour le contenu
    content_frame = tk.Frame(main_content_frame, bg=BACKGROUND_COLOR)
    content_frame.pack(side="right", fill="both", expand=True)

    # Variable pour suivre la catégorie active
    current_settings_category = "account"

    # Dictionnaire des boutons de catégories pour la gestion de l'état actif
    category_buttons = {}

    def show_category_content(category):
        """Affiche le contenu de la catégorie sélectionnée"""
        global current_settings_category
        current_settings_category = category

        # Mettre à jour l'apparence des boutons de catégories
        for cat, btn in category_buttons.items():
            if cat == category:
                btn.config(bg=ACTIVE_BUTTON_COLOR, relief="sunken")
            else:
                btn.config(bg=BACKGROUND_COLOR, relief="flat")

        # Effacer le contenu précédent
        for widget in content_frame.winfo_children():
            widget.destroy()

        if category == "account":
            show_account_settings(content_frame)
        elif category == "display":
            show_display_settings(content_frame)
        elif category == "version":
            show_general_settings(content_frame)

    # Créer les boutons de catégories
    categories = [
        ("account", "👤    Compte"),
        ("display", "🎨    Affichage"),
        ("version", " ℹ️À propos")
    ]

    for category_id, category_name in categories:
        btn = tk.Button(categories_frame, text=category_name,
                       command=lambda cat=category_id: show_category_content(cat),
                       font=("Helvetica", 11, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR,
                       relief="flat", anchor="w", padx=15, pady=12,
                       activebackground=ACTIVE_BUTTON_COLOR, activeforeground=TEXT_COLOR,
                       cursor="hand2")
        btn.pack(fill="x", pady=2)
        category_buttons[category_id] = btn

    # Afficher la catégorie par défaut (Compte)
    show_category_content("account")

    def on_close_settings():
        global current_settings_window
        if current_settings_window:
            current_settings_window.destroy()
        current_settings_window = None

    current_settings_window.protocol("WM_DELETE_WINDOW", on_close_settings)

def show_account_settings(parent_frame):
    """Affiche les paramètres de compte"""
    # Titre de la section
    title_label = tk.Label(parent_frame, text="Paramètres du compte:",
                          font=("Helvetica", 16, "bold"),
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(anchor="w", pady=(20, 20))

    # Informations de connexion
    info_frame = tk.Frame(parent_frame, bg=BACKGROUND_COLOR)
    info_frame.pack(fill="x", pady=(0, 20))

    if user_logged_in:
        # État connecté
        status_frame = tk.Frame(info_frame, bg=BACKGROUND_COLOR)
        status_frame.pack(fill="x", pady=(0, 15))

        status_text = f"✅ Connecté en tant que: {user_logged_name or 'Utilisateur'}"
        tk.Label(status_frame, text=status_text,
                font=("Helvetica", 11),
                bg=BACKGROUND_COLOR, fg="#27ae60").pack(anchor="w")

        # Bouton de déconnexion
        logout_btn = tk.Button(info_frame, text="🔓 Se déconnecter",
                              command=lambda: [logout_user(), current_settings_window.destroy()],
                              font=("Segoe UI Emoji", 12, "bold"), bg="#e74c3c", fg="white",
                              relief="flat", padx=25, pady=10, cursor="hand2",
                              activebackground="#c0392b", activeforeground="white")
        logout_btn.pack(anchor="w", pady=(10, 0))

    else:
        # État déconnecté
        status_frame = tk.Frame(info_frame, bg=BACKGROUND_COLOR)
        status_frame.pack(fill="x", pady=(0, 15))

        tk.Label(status_frame, text="❌ Non connecté",
                font=("Helvetica", 11),
                bg=BACKGROUND_COLOR, fg="#e74c3c").pack(anchor="w")

        # Message d'information
        info_text = "Vous devez être connecté pour accéder au store et télécharger des jeux."
        tk.Label(info_frame, text=info_text,
                font=("Helvetica", 10),
                bg=BACKGROUND_COLOR, fg="#7f8c8d",
                wraplength=400, justify="left").pack(anchor="w", pady=(0, 15))

        # Bouton de connexion
        login_btn = tk.Button(info_frame, text="🔐 Se connecter",
                             command=lambda: [show_login_dialog(), current_settings_window.destroy()],
                             font=("Segoe UI Emoji", 12, "bold"), bg="#27ae60", fg="white",
                             relief="flat", padx=25, pady=10, cursor="hand2",
                             activebackground="#229954", activeforeground="white")
        login_btn.pack(anchor="w")



def show_general_settings(parent_frame):
    """Affiche les paramètres généraux"""
    # Titre de la section
    title_label = tk.Label(parent_frame, text="À propos du launcher:",
                          font=("Helvetica", 16, "bold"),
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(anchor="w", pady=(20, 20))

    # Informations sur l'application
    info_frame = tk.Frame(parent_frame, bg=BACKGROUND_COLOR)
    info_frame.pack(fill="x", pady=(0, 20))

    tk.Label(info_frame, text="Cracken Launcher v1.2",
            font=("Helvetica", 11),
            bg=BACKGROUND_COLOR, fg="#7f8c8d").pack(anchor="w", pady=(5, 15))

    tk.Label(info_frame, text="Développé par Clemslegoat",
            font=("Helvetica", 11),
            bg=BACKGROUND_COLOR, fg="#7f8c8d").pack(anchor="w")

    # Section DMCA
    dmca_frame = tk.Frame(parent_frame, bg=BACKGROUND_COLOR)
    dmca_frame.pack(fill="x")

    # Bouton DMCA
    dmca_btn = tk.Button(dmca_frame, text="📄 DMCA",
                        command=lambda: open_dmca_link(),
                        font=("Segoe UI Emoji", 12, "bold"), bg="#34495e", fg="white",
                        relief="flat", padx=15, pady=8, cursor="hand2",
                        activebackground="#2c3e50", activeforeground="white")
    dmca_btn.pack(anchor="w", pady=(0, 10))



def show_display_settings(parent_frame):
    """Affiche les paramètres d'affichage"""
    global library_sort_mode

    # Titre de la section
    title_label = tk.Label(parent_frame, text="Paramètres d'affichage:",
                          font=("Helvetica", 16, "bold"),
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(anchor="w", pady=(20, 20))

    # Section tri de la bibliothèque
    sort_frame = tk.Frame(parent_frame, bg=BACKGROUND_COLOR)
    sort_frame.pack(fill="x", pady=(0, 20))

    tk.Label(sort_frame, text="• Tri de la bibliothèque:",
            font=("Helvetica", 13, "bold"),
            bg=BACKGROUND_COLOR, fg=TEXT_COLOR).pack(anchor="w", pady=(0, 15))

    # Variable pour les boutons radio
    sort_var = tk.StringVar(value=library_sort_mode)

    def on_sort_change():
        global library_sort_mode
        library_sort_mode = sort_var.get()
        save_sort_preference()
        update_games_grid()  # Rafraîchir l'affichage

    # Options de tri
    sort_options = [
        ("date_added", "Par date d'ajout (défaut)"),
        ("name", "Par nom alphabétique"),
        ("last_played", "Par dernier lancement")
    ]

    for value, text in sort_options:
        radio_btn = tk.Radiobutton(sort_frame, text=text, variable=sort_var, value=value,
                                  command=on_sort_change,
                                  font=("Helvetica", 11), bg=BACKGROUND_COLOR, fg=TEXT_COLOR,
                                  selectcolor=BACKGROUND_COLOR, activebackground=BACKGROUND_COLOR,
                                  activeforeground=TEXT_COLOR, cursor="hand2")
        radio_btn.pack(anchor="w", pady=2)

    # Description
    desc_label = tk.Label(sort_frame,
                         text="Choisissez comment organiser vos jeux dans la bibliothèque.",
                         font=("Helvetica", 10),
                         bg=BACKGROUND_COLOR, fg="#7f8c8d")
    desc_label.pack(anchor="w", pady=(15, 0))

def update_auth_ui():
    """Met à jour l'interface utilisateur selon l'état de connexion"""
    global settings_button, dmca_button

    # Créer le bouton paramètres s'il n'existe pas déjà
    if 'settings_button' not in globals() or not settings_button.winfo_exists():
        settings_button = tk.Button(top_frame, text="⚙️ Paramètres", command=show_settings_window,
                                  font=("Segoe UI Emoji", 11, "bold"), bg="#7f8c8d", fg="white",
                                  relief="flat", padx=15, pady=8, cursor="hand2",
                                  activebackground="#6c7b7d", activeforeground="white")
        settings_button.pack(side="right", padx=(0, 10))

    # Le bouton DMCA est maintenant dans la fenêtre paramètres, on le supprime d'ici
    # (mais on le garde pour la compatibilité avec le code existant)
    if 'dmca_button' in globals() and dmca_button and dmca_button.winfo_exists():
        dmca_button.destroy()

def download_torrent_from_url(url, dest_folder):
    """Télécharge un fichier .torrent depuis une URL directe et retourne le chemin local."""
    try:
        local_filename = os.path.join(dest_folder, url.split("/")[-1].split("?")[0])  # Nettoie le nom du fichier
        with requests.get(url, stream=True) as r:
            r.raise_for_status()
            with open(local_filename, 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    f.write(chunk)
        return local_filename
    except Exception as e:
        print(f"Erreur téléchargement : {e}")
        return None

def start_torrent_download(game_id, game_data):
    print(f"🚀 DEBUG: start_torrent_download() appelée pour {game_data.get('official_name', 'Jeu inconnu')}")
    print(f"🔍 DEBUG: game_id = {game_id}")
    print(f"🔍 DEBUG: Type du jeu = '{game_data.get('type', 'AUCUN TYPE')}'")

    torrent_url = game_data.get('torrent_url')
    if not torrent_url:
        messagebox.showinfo("Indisponible", "Le lien .torrent n'est pas encore renseigné pour ce jeu.")
        return

    temp_dir = tempfile.gettempdir()
    print(f"🔍 DEBUG: Téléchargement du torrent depuis: {torrent_url}")
    local_torrent = download_torrent_from_url(torrent_url, temp_dir)
    print(f"🔍 DEBUG: Torrent téléchargé: {local_torrent}")

    if not local_torrent or not is_valid_torrent_file(local_torrent):
        print(f"❌ DEBUG: Fichier torrent invalide")
        messagebox.showerror("Erreur", "Le fichier .torrent est indisponible pour le moment.")
        return
    print(f"✅ DEBUG: Fichier torrent validé")

    download_folder = filedialog.askdirectory(title="Choisissez le dossier de destination du jeu")
    print(f"🔍 DEBUG: Dossier choisi: {download_folder}")
    if not download_folder:
        print(f"❌ DEBUG: Aucun dossier choisi, arrêt")
        return
    print(f"✅ DEBUG: Dossier validé, continuation...")

    # AJOUTÉ: Créer une tâche de téléchargement et l'ajouter à notre liste globale.
    # C'est la clé pour que l'application sache qu'un téléchargement est actif.
    task_info = {
        'gid': None, # Le GID sera ajouté une fois le téléchargement démarré
        'name': game_data.get('official_name', 'Téléchargement'),
        'state': 'starting',
        'progress': 0,
        'download_speed': 0
    }
    download_tasks.append(task_info)


    def aria2_thread_with_progress():
        print(f"🚀 DEBUG: aria2_thread_with_progress() démarrée pour {game_data.get('official_name', 'Jeu inconnu')}")
        print(f"🔍 DEBUG: Type du jeu au début: '{game_data.get('type', 'AUCUN TYPE')}'")

        download = None
        try:
            # Lancer le téléchargement avec aria2p
            download = aria2.add_torrent(local_torrent, options={"dir": download_folder})
            task_info['gid'] = download.gid # Mettre à jour le GID dans notre tâche

            print(f"🔍 DEBUG: Téléchargement ajouté - GID: {download.gid}")
            print(f"🔍 DEBUG: Status initial: {download.status}")
            print(f"🔍 DEBUG: Début de la boucle de surveillance...")

            loop_count = 0
            while not download.is_complete and not download.has_failed and download.progress < 100:
                download.update() # Rafraîchir les infos du téléchargement

                loop_count += 1
                if loop_count % 20 == 0:  # Print toutes les 10 secondes (20 * 0.5s)
                    print(f"🔍 DEBUG: Boucle téléchargement - Status: {download.status}, Progress: {download.progress}%, Complete: {download.is_complete}, Failed: {download.has_failed}")

                # Mettre à jour les informations dans la liste globale
                task_info['state'] = download.status
                task_info['progress'] = download.progress
                task_info['download_speed'] = download.download_speed

                # Calculer les tailles en Go
                total_length_gb = download.total_length / (1024**3) if download.total_length > 0 else 0.0
                completed_length_gb = download.completed_length / (1024**3) if download.completed_length > 0 else 0.0

                # Mettre à jour la fenêtre de progression avec les tailles
                update_progress(
                    download.progress,
                    download.status,
                    download.download_speed / (1024 * 1024), # en Mo/s
                    completed_length_gb,  # Go téléchargés
                    total_length_gb       # Go total
                )

                if stop_flag.is_set():
                    aria2.remove([download], force=True)
                    task_info['state'] = "Annulé"
                    update_progress(download.progress, "Annulé", 0.0, completed_length_gb, total_length_gb)
                    break

                time.sleep(0.5)

            # Une fois la boucle terminée, mettre à jour le statut final
            print(f"🔍 DEBUG: Fin de boucle téléchargement")
            download.update()
            print(f"🔍 DEBUG: Status après update: {download.status}")
            print(f"🔍 DEBUG: Progress final: {download.progress}%")

            # Déterminer le status final
            final_download_status = download.status

            # Si le téléchargement est à 100%, attendre 3 secondes puis le considérer comme terminé
            if download.progress >= 100:
                print(f"✅ DEBUG: Téléchargement à 100%, attente de 3 secondes...")
                time.sleep(3)
                print(f"✅ DEBUG: Attente terminée, status forcé à 'complete'")
                final_download_status = "complete"

            task_info['state'] = final_download_status

            # Calculer les tailles finales
            final_total_gb = download.total_length / (1024**3) if download.total_length > 0 else 0.0
            final_completed_gb = download.completed_length / (1024**3) if download.completed_length > 0 else 0.0

            # État final selon le statut aria2
            final_state = "Téléchargement terminé" if final_download_status == "complete" else final_download_status
            print(f"🔍 DEBUG: Final state: {final_state}")

            update_progress(download.progress, final_state, 0.0, final_completed_gb, final_total_gb)

            # Extraction automatique pour les jeux online-fix
            print(f"🔍 DEBUG: Vérification extraction - Status: {final_download_status}")
            print(f"🔍 DEBUG: Game data: {game_data}")
            print(f"🔍 DEBUG: Type du jeu: '{game_data.get('type', 'AUCUN TYPE')}'")

            is_online_fix = is_online_fix_game(game_data)
            print(f"🔍 DEBUG: is_online_fix_game() retourne: {is_online_fix}")

            if final_download_status == "complete" and is_online_fix:
                print("✅ Jeu online-fix détecté - Démarrage de l'extraction automatique...")
                print(f"📁 DEBUG: Dossier de téléchargement: {download_folder}")

                def extraction_progress_callback(message, progress=0, current_file=""):
                    """Callback pour mettre à jour le statut d'extraction"""
                    print(f"📢 DEBUG: Message extraction: {message} - Progress: {progress}%")
                    # Toujours passer extraction_info pour activer le mode extraction
                    info_text = "extraction_active"  # Signal pour activer le mode extraction
                    print(f"🔍 DEBUG: Appel update_progress({progress}, {message}, 0.0, 0.0, 0.0, {info_text})")
                    update_progress(progress, message, 0.0, 0.0, 0.0, info_text)

                # Réinitialiser la barre pour l'extraction
                extraction_progress_callback("Préparation de l'extraction...", 0)

                # Lancer l'extraction automatique
                print("🚀 DEBUG: Appel de auto_extract_online_fix()...")
                extraction_success = auto_extract_online_fix(game_data, download_folder, extraction_progress_callback)
                print(f"🔍 DEBUG: Résultat extraction: {extraction_success}")

                if extraction_success:
                    final_state = "Téléchargement et extraction terminés !"
                    print("✅ DEBUG: Extraction réussie !")
                    extraction_progress_callback(final_state, 100)
                    print("📢 DEBUG: Message extraction: Téléchargement et extraction terminés !")

                    # Fermer la fenêtre après 3 secondes
                    print("🔒 DEBUG: Fermeture programmée dans 3 secondes...")
                    progress_win.after(3000, lambda: progress_win.destroy() if progress_win.winfo_exists() else None)
                else:
                    final_state = "Téléchargement terminé - Extraction échouée"
                    print("❌ DEBUG: Extraction échouée !")
                    extraction_progress_callback(final_state, 0)
                    print("📢 DEBUG: Message extraction: Extraction échouée !")

                    # Fermer la fenêtre après 5 secondes
                    print("🔒 DEBUG: Fermeture programmée dans 5 secondes...")
                    progress_win.after(5000, lambda: progress_win.destroy() if progress_win.winfo_exists() else None)

            else:
                print(f"⏭️  DEBUG: Pas d'extraction - Status: {final_download_status}, Online-fix: {is_online_fix}")

        except Exception as e:
            print(f"Erreur dans le thread aria2 : {e}")
            task_info['state'] = "Erreur"
            if 'update_progress' in locals():
                update_progress(0, f"Erreur: {e}", 0.0, 0.0, 0.0)
        finally:
            # EXPLICATION: Pour les téléchargements terminés avec succès, la fenêtre se ferme automatiquement
            # Pour les erreurs et annulations, on ferme manuellement après un délai
            if task_info.get('state') in ("Erreur", "Annulé") and 'progress_win' in locals() and progress_win.winfo_exists():
                # Attendre un peu pour que l'utilisateur voie le statut d'erreur/annulation
                time.sleep(2)
                if progress_win.winfo_exists():
                    progress_win.destroy()

            # Attendre un court instant pour s'assurer que l'état est bien mis à jour
            time.sleep(1)
            check_exit_after_download()


    # Afficher la fenêtre de progression
    stop_flag = threading.Event()
    progress_win, update_progress = show_download_progress_window(
        game_data.get('official_name', 'Téléchargement'),
        stop_callback=stop_flag.set
    )

    # Stocker la référence de la fenêtre pour l'extraction
    progress_window_ref = progress_win

    # Lancer le thread
    print(f"🚀 DEBUG: Lancement du thread de téléchargement...")
    threading.Thread(target=aria2_thread_with_progress, daemon=True).start()
    print(f"✅ DEBUG: Thread lancé")

def aria2_thread(game_id, game_data, local_torrent, download_folder):
    # ... code existant ...
    pass

def show_downloads_page(animate=True):
    global is_animating_downloads, downloads_refresh_after_id
    if 'is_animating_downloads' not in globals():
        is_animating_downloads = False
    if not is_animating_downloads and animate:
        is_animating_downloads = True
        start_downloads_animation()
    menu_height = top_frame.winfo_height()
    downloads_page_frame.place(x=0, y=menu_height, relwidth=1, relheight=1)
    for widget in downloads_page_frame.winfo_children():
        widget.destroy()
    title_label = tk.Label(downloads_page_frame, text="Gestionnaire de téléchargements", font=("Helvetica", 24, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(pady=20)
    if not download_tasks:
        empty_label = tk.Label(downloads_page_frame, text="Aucun téléchargement en cours.", font=("Helvetica", 14), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
        empty_label.pack(pady=40)
    else:
        for task in download_tasks:
            name = task.get('name', 'Téléchargement')
            progress = task.get('progress', 0)
            rate = task.get('download_rate', 0)
            state = task.get('state', '')
            frame = tk.Frame(downloads_page_frame, bg=BACKGROUND_COLOR)
            frame.pack(fill="x", padx=40, pady=10)
            tk.Label(frame, text=name, font=("Helvetica", 14, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR).pack(side="left")
            tk.Label(frame, text=f"{progress}%", font=("Helvetica", 12), bg=BACKGROUND_COLOR, fg=TEXT_COLOR).pack(side="left", padx=20)
            tk.Label(frame, text=f"{rate:.1f} kB/s", font=("Helvetica", 12), bg=BACKGROUND_COLOR, fg=TEXT_COLOR).pack(side="left", padx=20)
            tk.Label(frame, text=state, font=("Helvetica", 12), bg=BACKGROUND_COLOR, fg=TEXT_COLOR).pack(side="left", padx=20)
    # Pas d'animation - affichage direct
    # Pour le rafraîchissement, on ne veut pas d'animation
    if downloads_refresh_after_id:
        root.after_cancel(downloads_refresh_after_id)
    downloads_refresh_after_id = downloads_page_frame.after(1000, lambda: show_downloads_page(animate=False))

def start_downloads_animation():
    # ... code de l'animation ...
    end_downloads_animation()

def end_downloads_animation():
    global is_animating_downloads
    is_animating_downloads = False

def is_valid_torrent_file(filepath):
    try:
        with open(filepath, "rb") as f:
            header = f.read(20)
            return b"announce" in header or b"created by" in header
    except Exception:
        return False

def is_online_fix_game(game_data):
    """Vérifie si un jeu est un jeu online-fix basé sur le type dans la base de données"""
    game_type = game_data.get('type', '')

    # Seul type supporté pour l'extraction automatique
    return game_type == 'Online-Fix.Me'

def find_rar_archives(download_folder):
    """Trouve toutes les archives RAR dans le dossier de téléchargement"""
    print(f"🔍 DEBUG: find_rar_archives() - Dossier: {download_folder}")
    rar_files = []

    # Vérifier que le dossier existe
    if not os.path.exists(download_folder):
        print(f"❌ DEBUG: Le dossier {download_folder} n'existe pas")
        return rar_files

    print(f"✅ DEBUG: Le dossier existe, recherche récursive...")

    # Chercher récursivement dans tous les sous-dossiers
    for root, dirs, files in os.walk(download_folder):
        print(f"🔍 DEBUG: Scan dossier: {root}")
        print(f"🔍 DEBUG: Fichiers trouvés: {files}")

        for file in files:
            print(f"🔍 DEBUG: Vérification fichier: {file}")
            if file.lower().endswith('.rar'):
                full_path = os.path.join(root, file)
                rar_files.append(full_path)
                print(f"✅ DEBUG: Archive RAR trouvée: {full_path}")

    print(f"🔍 DEBUG: Total archives RAR trouvées: {len(rar_files)}")
    return rar_files

def find_main_rar_archive(rar_files):
    """Trouve l'archive RAR principale (part1 ou sans numéro de partie)"""
    if not rar_files:
        return None

    # Chercher d'abord les fichiers avec "part1" ou "part01"
    for rar_file in rar_files:
        filename = os.path.basename(rar_file).lower()
        if 'part1' in filename or 'part01' in filename or '.part1.' in filename or '.part01.' in filename:
            return rar_file

    # Si pas de part1, chercher le fichier sans numéro de partie
    for rar_file in rar_files:
        filename = os.path.basename(rar_file).lower()
        if 'part' not in filename:
            return rar_file

    # En dernier recours, prendre le premier fichier RAR trouvé
    return rar_files[0] if rar_files else None

def extract_online_fix_archive(rar_file, extract_to, progress_callback=None):
    """Extrait une archive RAR online-fix avec le mot de passe"""
    try:
        print(f"🔍 DEBUG: extract_online_fix_archive() appelée")
        print(f"🔍 DEBUG: rar_file = {rar_file}")
        print(f"🔍 DEBUG: extract_to = {extract_to}")
        print(f"🔍 DEBUG: rarfile.UNRAR_TOOL = {rarfile.UNRAR_TOOL}")

        # Mot de passe pour les archives online-fix
        password = "online-fix.me"
        print(f"🔍 DEBUG: Mot de passe utilisé: {password}")

        # Vérifier que le fichier RAR existe
        if not os.path.exists(rar_file):
            print(f"❌ DEBUG: Le fichier RAR n'existe pas: {rar_file}")
            return False

        print(f"✅ DEBUG: Fichier RAR existe, taille: {os.path.getsize(rar_file)} bytes")

        # Utiliser rarfile pour extraire
        print(f"🔍 DEBUG: Ouverture du fichier RAR avec rarfile...")
        with rarfile.RarFile(rar_file) as rf:
            print(f"✅ DEBUG: Fichier RAR ouvert avec succès")

            # Lister le contenu
            try:
                files = rf.namelist()
                print(f"✅ DEBUG: Contenu de l'archive: {len(files)} fichier(s)")
                for filename in files[:3]:
                    print(f"   - {filename}")
                if len(files) > 3:
                    print(f"   ... et {len(files) - 3} autres")

            except Exception as e:
                print(f"⚠️  DEBUG: Erreur listage contenu: {e}")

            # Vérifier si l'archive nécessite un mot de passe
            print(f"🔍 DEBUG: Configuration du mot de passe...")
            rf.setpassword(password)
            print(f"✅ DEBUG: Mot de passe configuré")

            # Extraire tous les fichiers avec progression réelle
            print(f"🔍 DEBUG: Extraction vers {extract_to}...")

            # Extraire fichier par fichier pour avoir la vraie progression
            total_files = len(files)
            for i, file_info in enumerate(rf.infolist()):
                if progress_callback:
                    progress_percent = int((i / total_files) * 100)
                    progress_callback("Extraction en cours...", progress_percent)

                # Extraire le fichier
                rf.extract(file_info, extract_to)
                print(f"🔍 DEBUG: Extrait {i+1}/{total_files}: {file_info.filename}")

            print(f"✅ DEBUG: Extraction terminée avec succès")

            if progress_callback:
                progress_callback("Finalisation...", 95)

        # Supprimer l'archive après extraction réussie
        try:
            print(f"🗑️  DEBUG: Suppression de l'archive: {rar_file}")
            os.remove(rar_file)
            print(f"✅ DEBUG: Archive supprimée avec succès")
            if progress_callback:
                progress_callback("Archive supprimée", 95)
        except Exception as e:
            print(f"⚠️  DEBUG: Erreur suppression archive: {e}")
            # Ne pas faire échouer l'extraction si la suppression échoue

        if progress_callback:
            progress_callback("Extraction terminée !", 100)

        return True

    except rarfile.RarWrongPassword:
        print(f"❌ DEBUG: Mot de passe incorrect pour {rar_file}")
        if progress_callback:
            progress_callback("Erreur: Mot de passe incorrect")
        return False
    except rarfile.RarCannotExec:
        print(f"❌ DEBUG: Impossible d'exécuter unrar. WinRAR: {rarfile.UNRAR_TOOL}")
        if progress_callback:
            progress_callback("Erreur: WinRAR non trouvé")
        return False
    except Exception as e:
        print(f"❌ DEBUG: Erreur lors de l'extraction de {rar_file}: {e}")
        import traceback
        traceback.print_exc()
        if progress_callback:
            progress_callback(f"Erreur d'extraction: {e}")
        return False

def auto_extract_online_fix(game_data, download_folder, progress_callback=None):
    """Extraction automatique pour les jeux online-fix"""
    try:
        print(f"🔍 DEBUG: auto_extract_online_fix() appelée")
        print(f"🔍 DEBUG: download_folder = {download_folder}")
        print(f"🔍 DEBUG: game_data = {game_data}")

        if progress_callback:
            progress_callback("Préparation...", 0)

        # Trouver toutes les archives RAR
        print(f"🔍 DEBUG: Recherche des archives RAR dans {download_folder}")
        rar_files = find_rar_archives(download_folder)
        print(f"🔍 DEBUG: find_rar_archives() retourne: {rar_files}")

        if not rar_files:
            print("❌ DEBUG: Aucune archive RAR trouvée")
            if progress_callback:
                progress_callback("Aucune archive trouvée", 0)
            return False

        print(f"✅ DEBUG: Archives RAR trouvées: {len(rar_files)}")
        for rar_file in rar_files:
            print(f"   - {rar_file}")

        # Trouver l'archive principale
        print(f"🔍 DEBUG: Recherche de l'archive principale...")
        main_rar = find_main_rar_archive(rar_files)
        print(f"🔍 DEBUG: find_main_rar_archive() retourne: {main_rar}")

        if not main_rar:
            print("❌ DEBUG: Archive principale non trouvée")
            if progress_callback:
                progress_callback("Archive principale non trouvée", 0)
            return False

        print(f"✅ DEBUG: Archive principale: {os.path.basename(main_rar)}")

        # Extraire l'archive principale
        print(f"🔍 DEBUG: Appel de extract_online_fix_archive()...")
        success = extract_online_fix_archive(main_rar, download_folder, progress_callback)
        print(f"🔍 DEBUG: extract_online_fix_archive() retourne: {success}")

        if success:
            print("✅ DEBUG: Extraction réussie !")
            if progress_callback:
                progress_callback("Extraction terminée !", 100)
            return True
        else:
            print("❌ DEBUG: Échec de l'extraction")
            if progress_callback:
                progress_callback("Échec de l'extraction", 0)
            return False

    except Exception as e:
        print(f"❌ DEBUG: Erreur lors de l'extraction automatique: {e}")
        import traceback
        traceback.print_exc()
        if progress_callback:
            progress_callback(f"Erreur: {e}")
        return False

def check_exit_after_download():
    """Vérifie si l'application doit se fermer après la fin d'un téléchargement."""
    global download_tasks

    # EXPLICATION: On filtre pour ne garder que les tâches réellement en cours.
    active_downloads = [
        task for task in download_tasks
        if task.get('state') not in ("complete", "error", "removed", "Terminé", "Annulé", "Erreur")
    ]

    # Si la liste des téléchargements actifs est vide ET que la fenêtre principale est cachée (parce que l'utilisateur l'a fermée)
    if not active_downloads and root.state() == 'withdrawn':
        print("Tous les téléchargements sont terminés. Fermeture de l'application.")

        # Arrêter aria2c proprement
        if aria2c_process is not None:
            try:
                aria2.remove_all(force=True)
                aria2c_process.terminate()
                aria2c_process.wait(timeout=2)
            except Exception as e:
                if aria2c_process.poll() is None:
                    aria2c_process.kill()

        # Forcer la fermeture complète de l'application.
        # os._exit(0) est utilisé pour garantir une sortie immédiate,
        # ce qui est utile si des threads Tkinter non-daemon sont encore actifs.
        os._exit(0)

class LauncherApp:
    def __init__(self, root):
        # ...
        pass

    def afficher_telechargements(self):
        # code pour afficher la page téléchargements
        pass

app = LauncherApp(root)

def show_download_progress_window(game_name, stop_callback=None):
    # Toujours utiliser Toplevel(root), même si root est caché
    progress_win = tk.Toplevel(root)
    progress_win.title("Gestionnaire de téléchargements")
    progress_win.configure(bg=BACKGROUND_COLOR)
    set_window_icon(progress_win)  # Appliquer l'icône personnalisée
    progress_win.resizable(False, False)
    progress_win.geometry("600x280")

    # Centrer la fenêtre
    progress_win.update_idletasks()
    width = progress_win.winfo_width()
    height = progress_win.winfo_height()
    x = (progress_win.winfo_screenwidth() // 2) - (width // 2)
    y = (progress_win.winfo_screenheight() // 2) - (height // 2)
    progress_win.geometry(f'{width}x{height}+{x}+{y}')

    # Titre du jeu
    title = tk.Label(progress_win, text=game_name, font=("Helvetica", 18, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title.pack(pady=(18, 10))

    # Barre de progression
    progress_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    progress_frame.pack(pady=(0, 10))
    progress_var = tk.DoubleVar(value=0)
    progress_bar = tk.Canvas(progress_frame, width=480, height=22, bg="#232728", highlightthickness=0)
    bar_rect = progress_bar.create_rectangle(0, 0, 0, 22, fill="#4be08a", width=0)
    progress_bar.pack()

    # Frame pour % et vitesse côte à côte, centré
    info_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    info_frame.pack(pady=(0, 2))
    percent_label = tk.Label(info_frame, text="0%", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    percent_label.pack(side="left", padx=(0, 10))
    speed_label = tk.Label(info_frame, text="0.0 Mo/s", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    speed_label.pack(side="left")

    # Affichage des Go téléchargés/total
    size_label = tk.Label(progress_win, text="0.00/0.00 Go", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    size_label.pack(pady=(5, 2))

    # État
    state_label = tk.Label(progress_win, text="Téléchargement en cours...", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    state_label.pack(pady=(0, 15))

    # Bouton Arrêter (taille d'origine)
    stop_btn = tk.Button(progress_win, text="Arrêter", font=("Helvetica", 12, "bold"), bg="#e74c3c", fg="#fff", relief="flat", padx=40, pady=3, command=stop_callback)
    stop_btn.pack(pady=(5, 0))

    # --- MODIFICATION ---
    # Nouvelle fonction pour gérer la confirmation
    def confirm_and_stop():
        # Affiche une boîte de dialogue "Oui/Non" avec la fenêtre de progression comme parent
        if messagebox.askyesno("Confirmation",
                               "Êtes-vous sûr de vouloir arrêter le téléchargement ?",
                               parent=progress_win):
            # Si l'utilisateur clique sur "Oui", on exécute le callback d'arrêt
            if stop_callback:
                stop_callback()
        # Si l'utilisateur clique sur "Non" ou ferme la boîte de dialogue, rien ne se passe.

    # Handler pour la croix (fermeture)
    if stop_callback is not None:
        # On remplace l'appel direct par notre fonction de confirmation
        progress_win.protocol("WM_DELETE_WINDOW", confirm_and_stop)

    def update_progress(percent, state, speed=0.0, downloaded_gb=0.0, total_gb=0.0, extraction_info=""):
        if progress_win.winfo_exists():
            percent = max(0, min(100, percent))
            progress_var.set(percent)
            progress_bar.coords(bar_rect, 0, 0, 4.8 * percent, 22)
            percent_label.config(text=f"{int(percent)}%")

            # Si on est en mode extraction
            if extraction_info:
                # Garder la vitesse vide (pas de "Extraction" à côté du %)
                speed_label.config(text="")
                # Cacher la ligne des Go
                size_label.config(text="")
                # Afficher "Extraction en cours..." dans le statut
                state_label.config(text="Extraction en cours...")
            else:
                # Mode téléchargement normal (comme avant)
                speed_label.config(text=f"{speed:.2f} Mo/s")

                # Mettre à jour l'affichage des Go téléchargés/total
                if total_gb > 0:
                    size_label.config(text=f"{downloaded_gb:.2f}/{total_gb:.2f} Go")
                else:
                    # Si pas de taille totale, calculer basé sur le pourcentage (fallback)
                    if downloaded_gb > 0:
                        estimated_total = downloaded_gb / (percent / 100) if percent > 0 else downloaded_gb
                        size_label.config(text=f"{downloaded_gb:.2f}/{estimated_total:.2f} Go")
                    else:
                        size_label.config(text="0.00/0.00 Go")

                # Afficher le statut normal
                state_label.config(text=state)

            progress_win.update_idletasks()

            # PAS de fermeture automatique - on laisse l'extraction gérer ça

    return progress_win, update_progress

def show_store_error_page():
    """Affiche une page d'erreur pour le store"""
    global downloads_refresh_after_id, store_search_frame

    if downloads_refresh_after_id:
        root.after_cancel(downloads_refresh_after_id)
        downloads_refresh_after_id = None

    # Masquer les autres frames et la barre de recherche de la bibliothèque
    main_frame.place_forget()
    search_frame.place_forget()
    stats_page_frame.place_forget()
    downloads_page_frame.place_forget()
    game_page_frame.place_forget()

    # Masquer la barre de recherche du store si elle existe déjà
    if 'store_search_frame' in globals() and store_search_frame:
        store_search_frame.place_forget()

    # Effacer le contenu précédent du store
    for widget in store_page_frame.winfo_children():
        widget.destroy()

    # Afficher la page du store
    store_page_frame.place(x=0, y=100, relwidth=1, relheight=1, height=-100)

    # Message d'erreur
    error_frame = tk.Frame(store_page_frame, bg=BACKGROUND_COLOR)
    error_frame.pack(expand=True, fill="both")

    tk.Label(error_frame, text="❌ Erreur de chargement",
             font=("Helvetica", 20, "bold"), bg=BACKGROUND_COLOR, fg="#e74c3c").pack(pady=(100, 20))

    tk.Label(error_frame, text="Impossible de charger les données du store.\nVérifiez votre connexion internet et réessayez.",
             font=("Helvetica", 12), bg=BACKGROUND_COLOR, fg=TEXT_COLOR, justify="center").pack(pady=10)

    # Bouton pour réessayer
    retry_button = tk.Button(error_frame, text="🔄 Réessayer",
                            command=lambda: retry_store_loading(),
                            font=("Helvetica", 12, "bold"), bg=BUTTON_COLOR, fg=TEXT_COLOR,
                            relief="flat", padx=20, pady=10)
    retry_button.pack(pady=20)

def retry_store_loading():
    """Réessaie de charger les données du store"""
    global store_loaded
    store_loaded = False  # Réinitialiser le flag
    handle_store_button()  # Relancer le processus de chargement

def show_store_page():
    global downloads_refresh_after_id, current_store_container, store_search_frame, store_search_entry, store_clear_button, cached_master_db
    if downloads_refresh_after_id:
        root.after_cancel(downloads_refresh_after_id)
        downloads_refresh_after_id = None

    # Masquer les autres frames et la barre de recherche de la bibliothèque
    main_frame.place_forget()
    search_frame.place_forget()
    stats_page_frame.place_forget()
    downloads_page_frame.place_forget()
    game_page_frame.place_forget()

    # Masquer la barre de recherche du store si elle existe déjà
    if 'store_search_frame' in globals() and store_search_frame:
        store_search_frame.place_forget()

    # Effacer le contenu précédent du store
    for widget in store_page_frame.winfo_children():
        widget.destroy()

    set_active_button(store_button)

    # Calculer les hauteurs
    menu_height = top_frame.winfo_height()

    # Positionner le store normalement (sans espace pour la barre de recherche au début)
    store_page_frame.place(x=0, y=menu_height, relwidth=1, relheight=1)

    # Pas d'animation pour le store

    # --- Titre du Store ---
    title_label = tk.Label(store_page_frame, text="Store", font=("Helvetica", 24, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(pady=(20, 10))

    # --- Barre de recherche du store (sous le titre) ---
    store_search_frame = tk.Frame(store_page_frame, bg=BACKGROUND_COLOR, height=50)
    store_search_frame.pack(fill="x", padx=20, pady=(0, 10))

    # Barre de recherche du store
    store_search_label = tk.Label(store_search_frame, text="🔍", font=("Segoe UI Emoji", 16), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    store_search_label.pack(side="left", padx=(20, 5), pady=10)

    store_search_entry = tk.Entry(store_search_frame, font=("Helvetica", 12), bg="#2c3e50", fg="#7f8c8d",
                                 insertbackground=TEXT_COLOR, relief="flat", bd=5)
    store_search_entry.pack(side="left", padx=(0, 10), pady=10, fill="x", expand=True)
    store_search_entry.insert(0, "Rechercher dans le store...")
    store_search_entry.bind('<KeyRelease>', on_store_search_change)
    store_search_entry.bind('<FocusIn>', on_store_search_focus_in)
    store_search_entry.bind('<FocusOut>', on_store_search_focus_out)

    store_clear_button = tk.Button(store_search_frame, text="✖", font=("Helvetica", 10), bg=HELP_BUTTON_COLOR,
                                  fg=TEXT_COLOR, relief="flat", padx=10, command=clear_store_search, cursor="hand2")
    store_clear_button.pack(side="right", padx=(0, 20), pady=10)

    # --- Message d'avertissement pour les suggestions ---
    warning_label = tk.Label(store_page_frame,
                           text="⚠️ Si le jeu que vous voulez n'est pas disponible, merci de faire une suggestion sur le Discord afin que je puisse l'ajouter au store. ⚠️",
                           font=("Helvetica", 10), bg=BACKGROUND_COLOR, fg="#f39c12",
                           wraplength=1200, justify="center")
    warning_label.pack(pady=(5, 15))

    # Bouton DMCA en bas du store (créé AVANT le conteneur des jeux)
    dmca_frame = tk.Frame(store_page_frame, bg=BACKGROUND_COLOR)
    dmca_frame.pack(side="bottom", pady=20)

    dmca_button = tk.Button(dmca_frame, text="📋 DMCA",
                           command=lambda: open_dmca_link(),
                           font=("Helvetica", 12, "bold"), bg="#e74c3c", fg="white",
                           relief="flat", padx=12, pady=6)
    dmca_button.pack()

    # Création d'un frame conteneur centré pour le contenu du store
    current_store_container = tk.Frame(store_page_frame, bg=BACKGROUND_COLOR)
    current_store_container.pack(fill="both", expand=True, padx=50)  # Marges latérales pour centrer

    # Afficher tous les jeux initialement (utilise le cache)
    display_store_games(current_store_container, cached_master_db)

def open_dmca_link():
    """Ouvre le lien DMCA dans le navigateur par défaut"""
    import webbrowser
    dmca_url = "https://faint-paddleboat-5de.notion.site/DMCA-225120b57f8b80239260fd071a144453#225120b57f8b8073b779f8e81ef524de"
    webbrowser.open(dmca_url)

def show_store_login_required_dialog():
    """Affiche une boîte de dialogue d'erreur indiquant qu'il faut être connecté"""
    messagebox.showerror("Connexion requise", "Vous devez être connecté pour pouvoir accéder au store.")

def handle_store_button():
    """Gère le clic sur le bouton Store avec authentification et chargement à la demande"""
    global user_logged_in, store_loaded, cached_master_db

    print(f"🔍 DEBUG: handle_store_button() appelée")
    print(f"🔍 DEBUG: user_logged_in = {user_logged_in}")
    print(f"🔍 DEBUG: store_loaded = {store_loaded}")

    if user_logged_in:
        # L'utilisateur est connecté, vérifier si le store est déjà chargé
        if not store_loaded:
            # Première ouverture du store, charger les données
            print("=== PREMIÈRE OUVERTURE DU STORE ===")
            print("Chargement des données du store...")
            preload_success = preload_store_data()
            if preload_success:
                store_loaded = True
                print("Chargement du store: RÉUSSI")
                show_store_page()
            else:
                print("Chargement du store: ÉCHEC")
                # Afficher une page d'erreur dans le store
                show_store_error_page()
        else:
            # Store déjà chargé, afficher directement
            show_store_page()
    else:
        # L'utilisateur n'est pas connecté, afficher un message d'information
        show_store_login_required_dialog()

store_button.config(command=handle_store_button)

# stats_button = tk.Button(top_frame, text="📊 Stats", command=show_stats_page, font=("Segoe UI Emoji", 14, "bold"), bg=BUTTON_COLOR, fg=TEXT_COLOR, relief="flat", padx=15, pady=5) # Command mis à jour
# stats_button.pack(side="left", padx=(5, 0)) # Ajustement du padding

# Initialisation sans pré-chargement du store
print("=== DÉMARRAGE DU LAUNCHER ===")
print("Nettoyage des anciens fichiers temporaires...")
cleanup_old_temp_files()

print("=== LAUNCHER PRÊT ===")

# Variable pour suivre si le store a été chargé
store_loaded = False

# Vérifier la connexion automatique au démarrage
auto_login_success = check_auto_login()
print(f"Connexion automatique: {'RÉUSSIE' if auto_login_success else 'AUCUNE'}")
update_auth_ui()

root.mainloop()