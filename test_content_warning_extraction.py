#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour extraire manuellement Content Warning
"""

import os
import tempfile

def test_content_warning_extraction():
    """Test l'extraction du fichier Content Warning spécifique"""
    
    print("🎮 TEST D'EXTRACTION - Content Warning")
    print("=" * 60)
    
    # Chemin vers le fichier RAR (à adapter)
    rar_file = r"C:\Users\<USER>\Downloads\Content.Warning.v1.17.b-OFME.rar"
    
    # Vérifier si le fichier existe
    if not os.path.exists(rar_file):
        print(f"❌ Fichier non trouvé: {rar_file}")
        print("📁 Veuillez modifier le chemin dans le script")
        return False
    
    print(f"✅ Fichier trouvé: {os.path.basename(rar_file)}")
    print(f"📊 Taille: {os.path.getsize(rar_file) / (1024*1024):.1f} MB")
    
    # Tester l'extraction
    try:
        import rarfile
        print("✅ Module rarfile disponible")
        
        # Créer un dossier temporaire pour l'extraction
        with tempfile.TemporaryDirectory() as temp_dir:
            extract_dir = os.path.join(temp_dir, "extracted")
            os.makedirs(extract_dir)
            
            print(f"📁 Dossier d'extraction: {extract_dir}")
            
            # Tenter l'extraction
            print("🔓 Tentative d'extraction...")
            
            with rarfile.RarFile(rar_file) as rf:
                # Essayer sans mot de passe d'abord
                try:
                    files = rf.namelist()
                    print(f"📋 Archive contient {len(files)} fichier(s)")
                    
                    # Afficher quelques fichiers
                    for i, filename in enumerate(files[:5]):
                        print(f"   - {filename}")
                    if len(files) > 5:
                        print(f"   ... et {len(files) - 5} autres fichiers")
                    
                    # Tenter extraction sans mot de passe
                    print("🔓 Extraction sans mot de passe...")
                    rf.extractall(extract_dir)
                    print("✅ Extraction réussie SANS mot de passe !")
                    
                except rarfile.RarWrongPassword:
                    print("🔑 Mot de passe requis, tentative avec 'online-fix.me'...")
                    rf.setpassword("online-fix.me")
                    
                    try:
                        rf.extractall(extract_dir)
                        print("✅ Extraction réussie AVEC mot de passe !")
                    except rarfile.RarWrongPassword:
                        print("❌ Mot de passe 'online-fix.me' incorrect")
                        
                        # Essayer d'autres mots de passe courants
                        other_passwords = ["", "123", "online-fix", "OFME", "Content.Warning"]
                        for pwd in other_passwords:
                            try:
                                rf.setpassword(pwd)
                                rf.extractall(extract_dir)
                                print(f"✅ Extraction réussie avec mot de passe: '{pwd}'")
                                break
                            except:
                                continue
                        else:
                            print("❌ Aucun mot de passe testé ne fonctionne")
                            return False
                
                # Vérifier les fichiers extraits
                extracted_files = []
                for root, dirs, files in os.walk(extract_dir):
                    for file in files:
                        extracted_files.append(os.path.join(root, file))
                
                print(f"📁 {len(extracted_files)} fichier(s) extrait(s)")
                
                # Chercher l'exécutable principal
                exe_files = [f for f in extracted_files if f.lower().endswith('.exe')]
                if exe_files:
                    print("🎮 Exécutable(s) trouvé(s):")
                    for exe in exe_files:
                        rel_path = os.path.relpath(exe, extract_dir)
                        print(f"   - {rel_path}")
                
                return True
                
    except ImportError:
        print("❌ Module rarfile non disponible")
        print("💡 Installez avec: pip install rarfile")
        return False
    except rarfile.RarCannotExec:
        print("❌ WinRAR/unrar non trouvé sur le système")
        print("💡 Installez WinRAR depuis winrar.com")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def check_game_type_in_db():
    """Vérifie quel type est configuré pour ce jeu dans la DB"""
    
    print(f"\n🔍 VÉRIFICATION DU TYPE DANS LA DB")
    print("=" * 60)
    
    print("Pour vérifier le type du jeu dans la base de données:")
    print("1. Ouvrir JSONBin.io")
    print("2. Chercher 'Content Warning' ou 'content-warning'")
    print("3. Vérifier le champ 'type'")
    print()
    print("Types possibles trouvés:")
    print("   ✅ 'Online-Fix.Me' → Extraction automatique")
    print("   ❌ 'Action' → Pas d'extraction")
    print("   ❌ 'Horror' → Pas d'extraction")
    print("   ❌ Autre type → Pas d'extraction")
    print()
    print("💡 Si le type n'est pas 'Online-Fix.Me', le changer dans la DB")

def simulate_launcher_detection():
    """Simule la détection du launcher"""
    
    print(f"\n🤖 SIMULATION DE LA DÉTECTION DU LAUNCHER")
    print("=" * 60)
    
    # Types possibles pour Content Warning
    possible_game_data = [
        {"official_name": "Content Warning", "type": "Online-Fix.Me"},
        {"official_name": "Content Warning", "type": "Horror"},
        {"official_name": "Content Warning", "type": "Action"},
        {"official_name": "Content Warning", "type": "Multiplayer"},
    ]
    
    def is_online_fix_game(game_data):
        return game_data.get('type', '') == 'Online-Fix.Me'
    
    print("Simulation avec différents types:")
    for game_data in possible_game_data:
        detected = is_online_fix_game(game_data)
        action = "EXTRAIRE" if detected else "IGNORER"
        status = "✅" if detected else "❌"
        print(f"   Type: '{game_data['type']}' → {action} {status}")

def main():
    """Fonction principale"""
    
    print("🚀 CRACKEN LAUNCHER - TEST CONTENT WARNING")
    print("=" * 70)
    print("Test d'extraction pour Content.Warning.v1.17.b-OFME.rar")
    print()
    
    # Modifier le chemin si nécessaire
    print("📝 AVANT DE COMMENCER:")
    print("Modifiez le chemin du fichier RAR dans le script si nécessaire")
    print("Chemin actuel: C:\\Users\\<USER>\\Downloads\\Content.Warning.v1.17.b-OFME.rar")
    print()
    
    # Lancer le test
    success = test_content_warning_extraction()
    check_game_type_in_db()
    simulate_launcher_detection()
    
    print("\n" + "="*70)
    if success:
        print("✅ EXTRACTION MANUELLE RÉUSSIE!")
        print("🔍 Vérifiez maintenant le type dans la base de données")
    else:
        print("❌ EXTRACTION MANUELLE ÉCHOUÉE")
        print("🔧 Vérifiez WinRAR et le module rarfile")
    
    print("\n🎯 ACTIONS À FAIRE:")
    print("1. Vérifier le type du jeu dans la DB JSONBin")
    print("2. S'assurer que c'est exactement 'Online-Fix.Me'")
    print("3. Relancer un téléchargement pour tester")
    print("4. Regarder les logs de la console du launcher")
    
    return success

if __name__ == "__main__":
    success = main()
    input(f"\n{'✅ Test réussi' if success else '❌ Test échoué'} - Appuyez sur Entrée pour continuer...")
