#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test standalone pour l'extraction online-fix
Permet de tester l'extraction sans télécharger de jeux
"""

import os
import tempfile
import shutil
import zipfile
import time
from pathlib import Path

# Simuler les fonctions du launcher
def is_online_fix_game(game_data):
    """Vérifie si un jeu est un jeu online-fix basé sur le type dans la base de données"""
    game_type = game_data.get('type', '').lower()
    
    # Types qui indiquent un jeu online-fix (plus précis que les mots-clés)
    online_fix_types = [
        'online-fix', 'onlinefix', 'online fix',
        'multiplayer fix', 'mp fix', 'lan fix',
        'steamworks fix', 'steam fix'
    ]
    
    # Vérifier si le type correspond exactement à un type online-fix
    return game_type in online_fix_types

def find_rar_archives(download_folder):
    """Trouve toutes les archives RAR dans le dossier de téléchargement"""
    rar_files = []
    
    # Chercher récursivement dans tous les sous-dossiers
    for root, dirs, files in os.walk(download_folder):
        for file in files:
            if file.lower().endswith('.rar'):
                rar_files.append(os.path.join(root, file))
    
    return rar_files

def find_main_rar_archive(rar_files):
    """Trouve l'archive RAR principale (part1 ou sans numéro de partie)"""
    if not rar_files:
        return None
    
    # Chercher d'abord les fichiers avec "part1" ou "part01"
    for rar_file in rar_files:
        filename = os.path.basename(rar_file).lower()
        if 'part1' in filename or 'part01' in filename or '.part1.' in filename or '.part01.' in filename:
            return rar_file
    
    # Si pas de part1, chercher le fichier sans numéro de partie
    for rar_file in rar_files:
        filename = os.path.basename(rar_file).lower()
        if 'part' not in filename:
            return rar_file
    
    # En dernier recours, prendre le premier fichier RAR trouvé
    return rar_files[0] if rar_files else None

def create_fake_rar_archives(test_folder, scenario="multi_part"):
    """Crée de fausses archives RAR pour les tests"""
    
    print(f"📁 Création du scénario: {scenario}")
    
    if scenario == "multi_part":
        # Scénario: Archives multi-parties
        fake_files = [
            "game.part1.rar",
            "game.part2.rar", 
            "game.part3.rar",
            "game.part4.rar"
        ]
    elif scenario == "single":
        # Scénario: Archive unique
        fake_files = ["game.rar"]
    elif scenario == "mixed":
        # Scénario: Mélange d'archives
        fake_files = [
            "setup.rar",
            "game.part1.rar",
            "game.part2.rar",
            "data.rar"
        ]
    elif scenario == "part01":
        # Scénario: Numérotation avec zéros
        fake_files = [
            "game.part01.rar",
            "game.part02.rar",
            "game.part03.rar"
        ]
    else:
        fake_files = ["unknown.rar"]
    
    created_files = []
    for filename in fake_files:
        filepath = os.path.join(test_folder, filename)
        
        # Créer un fichier vide qui simule une archive RAR
        with open(filepath, 'wb') as f:
            # Écrire un en-tête RAR basique pour que le fichier soit reconnu
            f.write(b'Rar!\x1a\x07\x00')  # Signature RAR
            f.write(b'\x00' * 100)  # Données factices
        
        created_files.append(filepath)
        print(f"   ✅ Créé: {filename}")
    
    return created_files

def simulate_extraction_test(game_data, test_folder):
    """Simule un test d'extraction complet"""
    
    print(f"\n🧪 Test d'extraction pour: {game_data['official_name']}")
    print("=" * 60)
    
    # 1. Vérifier si c'est un jeu online-fix
    is_online_fix = is_online_fix_game(game_data)
    print(f"1. Détection online-fix: {'✅ OUI' if is_online_fix else '❌ NON'}")
    print(f"   Type: '{game_data.get('type', 'Non spécifié')}'")
    
    if not is_online_fix:
        print("   ⏭️  Extraction ignorée (jeu normal)")
        return False
    
    # 2. Rechercher les archives RAR
    print("\n2. Recherche des archives RAR...")
    rar_files = find_rar_archives(test_folder)
    
    if not rar_files:
        print("   ❌ Aucune archive RAR trouvée")
        return False
    
    print(f"   ✅ {len(rar_files)} archive(s) trouvée(s):")
    for rar_file in rar_files:
        print(f"      - {os.path.basename(rar_file)}")
    
    # 3. Sélectionner l'archive principale
    print("\n3. Sélection de l'archive principale...")
    main_rar = find_main_rar_archive(rar_files)
    
    if not main_rar:
        print("   ❌ Archive principale non trouvée")
        return False
    
    print(f"   ✅ Archive sélectionnée: {os.path.basename(main_rar)}")
    
    # 4. Simuler l'extraction
    print("\n4. Simulation de l'extraction...")
    print("   🔓 Mot de passe: online-fix.me")
    print("   📦 Extraction en cours...")
    
    # Simuler un délai d'extraction
    for i in range(3):
        time.sleep(0.5)
        print(f"   {'.' * (i + 1)} {(i + 1) * 33}%")
    
    print("   ✅ Extraction simulée avec succès !")
    
    return True

def run_extraction_tests():
    """Lance une série de tests d'extraction"""
    
    print("🚀 Tests d'Extraction Online-Fix - Mode Standalone")
    print("=" * 70)
    
    # Jeux de test avec différents types
    test_games = [
        {
            "official_name": "Call of Duty Modern Warfare",
            "type": "online-fix",
            "description": "Jeu FPS multijoueur"
        },
        {
            "official_name": "FIFA 23",
            "type": "steamworks fix",
            "description": "Simulation de football"
        },
        {
            "official_name": "Grand Theft Auto V",
            "type": "multiplayer fix",
            "description": "Jeu d'action en monde ouvert"
        },
        {
            "official_name": "The Witcher 3",
            "type": "RPG",
            "description": "RPG solo épique"
        },
        {
            "official_name": "Minecraft",
            "type": "Sandbox",
            "description": "Jeu de construction"
        }
    ]
    
    # Scénarios d'archives
    scenarios = [
        ("multi_part", "Archives multi-parties (part1, part2, ...)"),
        ("single", "Archive unique"),
        ("mixed", "Mélange d'archives"),
        ("part01", "Numérotation avec zéros (part01, part02, ...)")
    ]
    
    results = []
    
    for game in test_games:
        for scenario_name, scenario_desc in scenarios:
            # Créer un dossier temporaire pour ce test
            with tempfile.TemporaryDirectory() as temp_dir:
                print(f"\n📂 Scénario: {scenario_desc}")
                
                # Créer les fausses archives
                created_files = create_fake_rar_archives(temp_dir, scenario_name)
                
                # Lancer le test d'extraction
                success = simulate_extraction_test(game, temp_dir)
                
                results.append({
                    'game': game['official_name'],
                    'type': game['type'],
                    'scenario': scenario_desc,
                    'success': success
                })
                
                print("-" * 60)
    
    # Résumé des résultats
    print("\n📊 RÉSUMÉ DES TESTS")
    print("=" * 70)
    
    online_fix_games = [r for r in results if is_online_fix_game({'type': r['type']})]
    normal_games = [r for r in results if not is_online_fix_game({'type': r['type']})]
    
    print(f"🎮 Jeux online-fix testés: {len(online_fix_games)}")
    print(f"🎮 Jeux normaux testés: {len(normal_games)}")
    
    print(f"\n✅ Extractions réussies: {sum(1 for r in results if r['success'])}")
    print(f"⏭️  Extractions ignorées: {sum(1 for r in results if not r['success'])}")
    
    # Détail par type
    print(f"\n📋 DÉTAIL PAR TYPE:")
    types_tested = set(r['type'] for r in results)
    for game_type in sorted(types_tested):
        type_results = [r for r in results if r['type'] == game_type]
        is_online_fix_type = is_online_fix_game({'type': game_type})
        status = "🔓 ONLINE-FIX" if is_online_fix_type else "🎮 NORMAL"
        print(f"   {game_type:15} → {status}")
    
    return results

def create_sample_database_with_types():
    """Crée un exemple de base de données avec les bons types"""
    
    print("\n📝 EXEMPLE DE BASE DE DONNÉES AVEC TYPES")
    print("=" * 70)
    
    sample_db = {
        "cod_mw": {
            "official_name": "Call of Duty Modern Warfare",
            "description": "Jeu de tir multijoueur",
            "type": "online-fix",  # ← Type précis pour online-fix
            "image_url": "https://example.com/cod.jpg",
            "exe_name": "ModernWarfare.exe",
            "torrent_url": "https://example.com/cod.torrent"
        },
        "fifa23": {
            "official_name": "FIFA 23",
            "description": "Simulation de football",
            "type": "steamworks fix",  # ← Type précis pour steamworks
            "image_url": "https://example.com/fifa.jpg",
            "exe_name": "FIFA23.exe",
            "torrent_url": "https://example.com/fifa.torrent"
        },
        "gta5": {
            "official_name": "Grand Theft Auto V",
            "description": "Jeu d'action en monde ouvert",
            "type": "multiplayer fix",  # ← Type précis pour multiplayer
            "image_url": "https://example.com/gta5.jpg",
            "exe_name": "GTA5.exe",
            "torrent_url": "https://example.com/gta5.torrent"
        },
        "witcher3": {
            "official_name": "The Witcher 3",
            "description": "RPG solo épique",
            "type": "RPG",  # ← Type normal (pas d'extraction)
            "image_url": "https://example.com/witcher.jpg",
            "exe_name": "witcher3.exe",
            "torrent_url": "https://example.com/witcher.torrent"
        }
    }
    
    import json
    print(json.dumps(sample_db, indent=2, ensure_ascii=False))
    
    print(f"\n🔓 Types online-fix détectés:")
    online_fix_types = [
        'online-fix', 'onlinefix', 'online fix',
        'multiplayer fix', 'mp fix', 'lan fix',
        'steamworks fix', 'steam fix'
    ]
    for fix_type in online_fix_types:
        print(f"   - '{fix_type}'")
    
    return sample_db

if __name__ == "__main__":
    print("🔧 CRACKEN LAUNCHER - TEST EXTRACTION STANDALONE")
    print("=" * 70)
    print("Ce script teste l'extraction online-fix sans téléchargement")
    print("Il simule des archives RAR et teste la détection/extraction")
    print()
    
    # Lancer tous les tests
    results = run_extraction_tests()
    
    # Créer l'exemple de base de données
    create_sample_database_with_types()
    
    print("\n" + "="*70)
    print("✨ AVANTAGES DE LA NOUVELLE MÉTHODE:")
    print("   1. ✅ Détection basée sur le type exact dans la DB")
    print("   2. ✅ Plus précis que la recherche par mots-clés")
    print("   3. ✅ Facile à configurer dans la base de données")
    print("   4. ✅ Tests sans téléchargement nécessaire")
    print()
    print("🎯 TYPES ONLINE-FIX À UTILISER DANS LA DB:")
    print("   - 'online-fix' (recommandé)")
    print("   - 'steamworks fix'")
    print("   - 'multiplayer fix'")
    print("   - 'lan fix'")
    print()
    print("🎮 Prêt pour utilisation en production!")
    
    input("\nAppuyez sur Entrée pour continuer...")
