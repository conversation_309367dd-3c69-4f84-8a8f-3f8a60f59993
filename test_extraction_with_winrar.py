#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier l'extraction avec WinRAR configuré
"""

import os
import tempfile
import subprocess

def test_rarfile_with_winrar():
    """Test rarfile avec WinRAR configuré"""
    
    print("🧪 TEST RARFILE AVEC WINRAR CONFIGURÉ")
    print("=" * 60)
    
    # Configuration rarfile
    try:
        import rarfile
        rarfile.UNRAR_TOOL = r"C:\Program Files\WinRAR\WinRAR.exe"
        print("✅ Configuration rarfile appliquée")
        print(f"   Outil: {rarfile.UNRAR_TOOL}")
    except ImportError:
        print("❌ Module rarfile non disponible")
        return False
    
    # Créer un fichier de test et une archive
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"\n📁 Dossier temporaire: {temp_dir}")
        
        # Créer un fichier de test
        test_file = os.path.join(temp_dir, "test_game.exe")
        with open(test_file, 'w') as f:
            f.write("Fake game executable for testing online-fix extraction")
        
        print("✅ Fichier de test créé")
        
        # Créer une archive RAR avec WinRAR
        rar_file = os.path.join(temp_dir, "test_online_fix.rar")
        
        try:
            # Commande WinRAR pour créer l'archive avec mot de passe
            cmd = [
                r"C:\Program Files\WinRAR\WinRAR.exe",
                "a",  # ajouter
                "-ponline-fix.me",  # mot de passe
                rar_file,
                test_file
            ]
            
            print("🔧 Création d'archive RAR avec mot de passe...")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Archive RAR créée avec succès")
                print(f"   Taille: {os.path.getsize(rar_file)} bytes")
                
                # Tester l'extraction avec rarfile
                extract_dir = os.path.join(temp_dir, "extracted")
                os.makedirs(extract_dir)
                
                print("\n🔓 Test d'extraction avec rarfile...")
                
                try:
                    with rarfile.RarFile(rar_file) as rf:
                        # Lister le contenu
                        files = rf.namelist()
                        print(f"📋 Contenu de l'archive: {files}")
                        
                        # Définir le mot de passe
                        rf.setpassword("online-fix.me")
                        
                        # Extraire
                        rf.extractall(extract_dir)
                        
                        # Vérifier l'extraction
                        extracted_file = os.path.join(extract_dir, "test_game.exe")
                        if os.path.exists(extracted_file):
                            print("✅ Extraction réussie !")
                            
                            # Vérifier le contenu
                            with open(extracted_file, 'r') as f:
                                content = f.read()
                            
                            if "Fake game executable" in content:
                                print("✅ Contenu du fichier correct")
                                return True
                            else:
                                print("❌ Contenu du fichier incorrect")
                        else:
                            print("❌ Fichier non extrait")
                            
                except rarfile.RarWrongPassword:
                    print("❌ Mot de passe incorrect")
                except rarfile.RarCannotExec:
                    print("❌ Impossible d'exécuter WinRAR")
                except Exception as e:
                    print(f"❌ Erreur extraction: {e}")
                    
            else:
                print(f"❌ Erreur création archive: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Erreur test: {e}")
    
    return False

def simulate_online_fix_extraction():
    """Simule l'extraction d'un jeu online-fix"""
    
    print(f"\n🎮 SIMULATION EXTRACTION ONLINE-FIX")
    print("=" * 60)
    
    # Simuler les fonctions du launcher
    def is_online_fix_game(game_data):
        return game_data.get('type', '') == 'Online-Fix.Me'
    
    def find_rar_archives(folder):
        rar_files = []
        for root, dirs, files in os.walk(folder):
            for file in files:
                if file.lower().endswith('.rar'):
                    rar_files.append(os.path.join(root, file))
        return rar_files
    
    def find_main_rar_archive(rar_files):
        if not rar_files:
            return None
        
        # Chercher part1
        for rar_file in rar_files:
            filename = os.path.basename(rar_file).lower()
            if 'part1' in filename or 'part01' in filename:
                return rar_file
        
        # Sinon prendre le premier
        return rar_files[0]
    
    # Simuler Content Warning
    game_data = {
        "official_name": "Content Warning",
        "type": "Online-Fix.Me"
    }
    
    print("1. 🔍 Vérification du type de jeu...")
    is_online_fix = is_online_fix_game(game_data)
    print(f"   Type: {game_data['type']}")
    print(f"   Online-Fix détecté: {'✅ OUI' if is_online_fix else '❌ NON'}")
    
    if is_online_fix:
        print("\n2. 📁 Recherche des archives RAR...")
        
        # Simuler un dossier avec des archives
        with tempfile.TemporaryDirectory() as temp_dir:
            # Créer des fausses archives
            fake_archives = [
                "Content.Warning.v1.17.b-OFME.rar",
                "setup.rar"
            ]
            
            for archive_name in fake_archives:
                archive_path = os.path.join(temp_dir, archive_name)
                with open(archive_path, 'wb') as f:
                    f.write(b'Rar!\x1a\x07\x00')  # Signature RAR
                
                print(f"   ✅ Archive trouvée: {archive_name}")
            
            # Trouver l'archive principale
            rar_files = find_rar_archives(temp_dir)
            main_rar = find_main_rar_archive(rar_files)
            
            if main_rar:
                print(f"\n3. 🎯 Archive principale sélectionnée:")
                print(f"   {os.path.basename(main_rar)}")
                
                print(f"\n4. 🔓 Extraction simulée...")
                print(f"   Mot de passe: online-fix.me")
                print(f"   ✅ Extraction terminée !")
                
                return True
    
    return False

def main():
    """Fonction principale"""
    
    print("🚀 CRACKEN LAUNCHER - TEST EXTRACTION AVEC WINRAR")
    print("=" * 70)
    print("Test de l'extraction avec WinRAR configuré")
    print()
    
    # Test rarfile avec WinRAR
    rarfile_test = test_rarfile_with_winrar()
    
    # Simulation extraction online-fix
    simulation_test = simulate_online_fix_extraction()
    
    print("\n" + "="*70)
    print("📋 RÉSUMÉ DES TESTS:")
    print(f"   Test rarfile + WinRAR: {'✅' if rarfile_test else '❌'}")
    print(f"   Simulation online-fix: {'✅' if simulation_test else '❌'}")
    
    if rarfile_test and simulation_test:
        print("\n✅ TOUS LES TESTS RÉUSSIS!")
        print("🎮 L'extraction automatique devrait maintenant fonctionner")
        print("🔧 Configuration WinRAR appliquée au launcher")
        
        print(f"\n🎯 PROCHAINES ÉTAPES:")
        print("1. Relancer le launcher")
        print("2. Télécharger Content Warning")
        print("3. Vérifier l'extraction automatique")
        print("4. Regarder les messages dans la console")
        
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        if not rarfile_test:
            print("🔧 Problème avec rarfile + WinRAR")
        if not simulation_test:
            print("🔧 Problème avec la simulation")
    
    print(f"\n💡 CONFIGURATION APPLIQUÉE AU LAUNCHER:")
    print(f"   rarfile.UNRAR_TOOL = r\"C:\\Program Files\\WinRAR\\WinRAR.exe\"")
    
    return rarfile_test and simulation_test

if __name__ == "__main__":
    success = main()
    input(f"\n{'✅ Configuration OK' if success else '❌ Problème config'} - Appuyez sur Entrée pour continuer...")
