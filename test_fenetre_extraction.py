#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour la fenêtre d'extraction
Permet de tester et modifier l'interface d'extraction facilement
"""

import tkinter as tk
from tkinter import ttk
import time
import threading

# Couleurs du thème
BACKGROUND_COLOR = "#0B1317"
TEXT_COLOR = "#FFFFFF"
BUTTON_COLOR = "#44C283"
PROGRESS_COLOR = "#44C283"

def create_extraction_window():
    """Crée la fenêtre d'extraction"""
    
    # Créer la fenêtre
    progress_win = tk.Toplevel()
    progress_win.title("Content Warning - Extraction")
    progress_win.geometry("500x200")
    progress_win.configure(bg=BACKGROUND_COLOR)
    progress_win.resizable(False, False)
    
    # Centrer la fenêtre
    progress_win.update_idletasks()
    x = (progress_win.winfo_screenwidth() // 2) - (500 // 2)
    y = (progress_win.winfo_screenheight() // 2) - (200 // 2)
    progress_win.geometry(f"500x200+{x}+{y}")
    
    # Titre du jeu
    title_label = tk.Label(progress_win, text="Content Warning", 
                          font=("Helvetica", 16, "bold"), 
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title_label.pack(pady=(20, 10))
    
    # Frame pour la barre de progression
    progress_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    progress_frame.pack(pady=10)
    
    # Canvas pour la barre de progression personnalisée
    progress_canvas = tk.Canvas(progress_frame, width=480, height=25, 
                               bg="#2A2A2A", highlightthickness=1, 
                               highlightbackground="#555555")
    progress_canvas.pack()
    
    # Rectangle de la barre de progression
    bar_rect = progress_canvas.create_rectangle(2, 2, 2, 23, 
                                               fill=PROGRESS_COLOR, outline="")
    
    # Labels d'information
    info_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    info_frame.pack(pady=10, fill="x", padx=20)
    
    # Pourcentage et vitesse/mode
    top_info_frame = tk.Frame(info_frame, bg=BACKGROUND_COLOR)
    top_info_frame.pack(fill="x")
    
    percent_label = tk.Label(top_info_frame, text="0%", 
                            font=("Helvetica", 12, "bold"), 
                            bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    percent_label.pack(side="left")
    
    speed_label = tk.Label(top_info_frame, text="Extraction", 
                          font=("Helvetica", 10), 
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    speed_label.pack(side="right")
    
    # Taille et statut
    bottom_info_frame = tk.Frame(info_frame, bg=BACKGROUND_COLOR)
    bottom_info_frame.pack(fill="x", pady=(5, 0))
    
    size_label = tk.Label(bottom_info_frame, text="Préparation...", 
                         font=("Helvetica", 10), 
                         bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    size_label.pack(side="left")
    
    # Statut principal
    state_label = tk.Label(progress_win, text="Préparation de l'extraction...", 
                          font=("Helvetica", 11), 
                          bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    state_label.pack(pady=(10, 20))
    
    # Bouton d'arrêt (optionnel)
    stop_button = tk.Button(progress_win, text="Arrêter", 
                           font=("Helvetica", 10), 
                           bg="#FF4444", fg=TEXT_COLOR,
                           relief="flat", padx=20, pady=5,
                           command=lambda: progress_win.destroy())
    stop_button.pack(side="bottom", pady=(0, 10))
    
    def update_progress(percent, state, info_text=""):
        """Met à jour la progression"""
        if progress_win.winfo_exists():
            # Limiter le pourcentage
            percent = max(0, min(100, percent))
            
            # Mettre à jour la barre
            bar_width = (480 - 4) * (percent / 100)
            progress_canvas.coords(bar_rect, 2, 2, 2 + bar_width, 23)
            
            # Mettre à jour les labels
            percent_label.config(text=f"{int(percent)}%")
            state_label.config(text=state)
            
            if info_text:
                size_label.config(text=info_text)
            
            # Rafraîchir l'affichage
            progress_win.update_idletasks()
            
            print(f"🔍 DEBUG: Progression mise à jour - {percent}% - {state} - {info_text}")
    
    return progress_win, update_progress

def simulate_extraction(update_progress_func):
    """Simule une extraction avec progression"""
    
    steps = [
        (0, "Préparation de l'extraction...", "Initialisation"),
        (10, "Recherche des archives...", "Scan du dossier"),
        (20, "Sélection de l'archive principale...", "Archive trouvée"),
        (30, "Ouverture de l'archive...", "Content.Warning.v1.17.b-OFME.rar"),
        (40, "Analyse du contenu...", "Vérification mot de passe"),
        (50, "Extraction des fichiers...", "150 fichiers trouvés"),
        (60, "Extraction en cours...", "Content Warning.exe"),
        (70, "Extraction en cours...", "data/level1.dat"),
        (80, "Extraction en cours...", "data/textures.pak"),
        (90, "Finalisation...", "Nettoyage"),
        (100, "Extraction terminée !", "Tous les fichiers extraits")
    ]
    
    for percent, state, info in steps:
        update_progress_func(percent, state, info)
        time.sleep(1)  # Attendre 1 seconde entre chaque étape
    
    # Attendre 3 secondes puis fermer
    time.sleep(3)
    print("🔒 Fermeture de la fenêtre...")

def main():
    """Fonction principale"""
    
    print("🚀 TEST FENÊTRE D'EXTRACTION")
    print("=" * 50)
    print("Création de la fenêtre d'extraction pour Content Warning")
    print()
    
    # Créer la fenêtre principale (cachée)
    root = tk.Tk()
    root.withdraw()  # Cacher la fenêtre principale
    
    # Créer la fenêtre d'extraction
    progress_win, update_progress = create_extraction_window()
    
    print("✅ Fenêtre d'extraction créée")
    print("🔄 Démarrage de la simulation...")
    
    # Lancer la simulation dans un thread
    def run_simulation():
        time.sleep(1)  # Attendre que la fenêtre s'affiche
        simulate_extraction(update_progress)
        # Fermer la fenêtre après simulation
        if progress_win.winfo_exists():
            progress_win.destroy()
        root.quit()
    
    simulation_thread = threading.Thread(target=run_simulation, daemon=True)
    simulation_thread.start()
    
    print("👀 Regardez la fenêtre d'extraction en action !")
    print("⏱️  La simulation dure environ 15 secondes")
    
    # Lancer la boucle principale
    root.mainloop()
    
    print("\n✅ Test terminé !")
    print("💡 Vous pouvez modifier ce script pour tester différents designs")

if __name__ == "__main__":
    main()
