#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour la fenêtre d'extraction
Copie exacte de la fenêtre du launcher pour tests et modifications
"""

import tkinter as tk
from tkinter import messagebox
import time
import threading

# Couleurs exactes du launcher
BACKGROUND_COLOR = "#0B1317"
TEXT_COLOR = "#FFFFFF"

def create_extraction_window():
    """Crée la fenêtre d'extraction - COPIE EXACTE du launcher"""

    # Créer la fenêtre principale cachée (comme dans le launcher)
    root = tk.Tk()
    root.withdraw()

    # Créer la fenêtre de progression (copie exacte)
    progress_win = tk.Toplevel(root)
    progress_win.title("Gestionnaire de téléchargements")
    progress_win.configure(bg=BACKGROUND_COLOR)
    progress_win.resizable(False, False)
    progress_win.geometry("600x280")

    # Centrer la fenêtre (copie exacte)
    progress_win.update_idletasks()
    width = progress_win.winfo_width()
    height = progress_win.winfo_height()
    x = (progress_win.winfo_screenwidth() // 2) - (width // 2)
    y = (progress_win.winfo_screenheight() // 2) - (height // 2)
    progress_win.geometry(f'{width}x{height}+{x}+{y}')

    # Titre du jeu (copie exacte)
    title = tk.Label(progress_win, text="Content Warning", font=("Helvetica", 18, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title.pack(pady=(18, 10))

    # Barre de progression (copie exacte)
    progress_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    progress_frame.pack(pady=(0, 10))
    progress_var = tk.DoubleVar(value=0)
    progress_bar = tk.Canvas(progress_frame, width=480, height=22, bg="#232728", highlightthickness=0)
    bar_rect = progress_bar.create_rectangle(0, 0, 0, 22, fill="#4be08a", width=0)
    progress_bar.pack()

    # Frame pour % et vitesse côte à côte, centré (copie exacte)
    info_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    info_frame.pack(pady=(0, 2))
    percent_label = tk.Label(info_frame, text="0%", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    percent_label.pack(side="left", padx=(0, 10))
    speed_label = tk.Label(info_frame, text="0.0 Mo/s", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    speed_label.pack(side="left")

    # Affichage des Go téléchargés/total (normal pour téléchargement)
    size_label = tk.Label(progress_win, text="0.00/0.00 Go", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    size_label.pack(pady=(5, 2))

    # État (normal pour téléchargement, remplacé par "Extraction en cours..." pour extraction)
    state_label = tk.Label(progress_win, text="active", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    state_label.pack(pady=(0, 15))

    # Bouton Arrêter (copie exacte)
    def stop_callback():
        progress_win.destroy()
        root.quit()

    stop_btn = tk.Button(progress_win, text="Arrêter", font=("Helvetica", 12, "bold"), bg="#e74c3c", fg="#fff", relief="flat", padx=40, pady=3, command=stop_callback)
    stop_btn.pack(pady=(5, 0))

    # Fonction update_progress modifiée
    def update_progress(percent, state, speed=0.0, downloaded_gb=0.0, total_gb=0.0, extraction_info=""):
        if progress_win.winfo_exists():
            percent = max(0, min(100, percent))
            progress_var.set(percent)
            progress_bar.coords(bar_rect, 0, 0, 4.8 * percent, 22)
            percent_label.config(text=f"{int(percent)}%")

            # Si on est en mode extraction
            if extraction_info:
                # Garder la vitesse vide (pas de "Extraction" à côté du %)
                speed_label.config(text="")
                # Cacher la ligne des Go
                size_label.config(text="")
                # Afficher "Extraction en cours..." dans le statut
                state_label.config(text="Extraction en cours...")
            else:
                # Mode téléchargement normal (comme avant)
                speed_label.config(text=f"{speed:.2f} Mo/s")

                # Afficher les Go téléchargés/total
                if total_gb > 0:
                    size_label.config(text=f"{downloaded_gb:.2f}/{total_gb:.2f} Go")
                else:
                    # Si pas de taille totale, calculer basé sur le pourcentage (fallback)
                    if downloaded_gb > 0:
                        estimated_total = downloaded_gb / (percent / 100) if percent > 0 else downloaded_gb
                        size_label.config(text=f"{downloaded_gb:.2f}/{estimated_total:.2f} Go")
                    else:
                        size_label.config(text="0.00/0.00 Go")

                # Afficher le statut normal
                state_label.config(text=state)

            progress_win.update_idletasks()

            print(f"🔍 DEBUG: Progression mise à jour - {percent}% - {state} - Extraction: {'Oui' if extraction_info else 'Non'}")

    return root, progress_win, update_progress

def simulate_extraction(update_progress_func):
    """Simule une extraction avec progression - comme dans le launcher"""

    # Phase 1: Téléchargement terminé
    print("📥 Simulation: Téléchargement terminé")
    update_progress_func(100, "Téléchargement terminé", 45.2, 52.94, 52.94)
    time.sleep(2)

    # Phase 2: Transition vers extraction
    print("🔄 Simulation: Début extraction")
    steps = [
        (0, "Préparation de l'extraction...", 0.0, 0.0, 0.0, "Initialisation"),
        (10, "Recherche des archives...", 0.0, 0.0, 0.0, "Scan du dossier"),
        (20, "Sélection de l'archive principale...", 0.0, 0.0, 0.0, "Archive trouvée"),
        (30, "Extraction de Content.Warning.v1.17.b-OFME.rar...", 0.0, 0.0, 0.0, "Fichier: Content.Warning.v1.17.b-OFME.rar"),
        (40, "Ouverture de l'archive...", 0.0, 0.0, 0.0, "Vérification mot de passe"),
        (50, "Analyse du contenu...", 0.0, 0.0, 0.0, "150 fichiers trouvés"),
        (60, "Extraction de 150 fichier(s)...", 0.0, 0.0, 0.0, "150 fichiers trouvés"),
        (70, "Extraction des fichiers...", 0.0, 0.0, 0.0, "Content Warning.exe"),
        (80, "Extraction des fichiers...", 0.0, 0.0, 0.0, "data/level1.dat"),
        (90, "Finalisation...", 0.0, 0.0, 0.0, "Nettoyage"),
        (95, "Archive supprimée", 0.0, 0.0, 0.0, "Archive supprimée"),
        (100, "Téléchargement et extraction terminés !", 0.0, 0.0, 0.0, "Tous les fichiers extraits")
    ]

    for percent, state, speed, downloaded, total, info in steps:
        update_progress_func(percent, state, speed, downloaded, total, info)
        time.sleep(1)  # Attendre 1 seconde entre chaque étape

    # Attendre 3 secondes puis fermer
    print("📢 DEBUG: Message extraction: Téléchargement et extraction terminés !")
    time.sleep(3)
    print("🔒 Fermeture de la fenêtre...")

def main():
    """Fonction principale"""

    print("🚀 TEST FENÊTRE D'EXTRACTION - COPIE EXACTE DU LAUNCHER")
    print("=" * 60)
    print("Fenêtre identique à celle du launcher avec simulation d'extraction")
    print()

    # Créer la fenêtre d'extraction (copie exacte)
    root, progress_win, update_progress = create_extraction_window()

    print("✅ Fenêtre d'extraction créée (copie exacte du launcher)")
    print("🔄 Démarrage de la simulation...")

    # Lancer la simulation dans un thread
    def run_simulation():
        time.sleep(1)  # Attendre que la fenêtre s'affiche
        simulate_extraction(update_progress)
        # Fermer la fenêtre après simulation
        if progress_win.winfo_exists():
            progress_win.destroy()
        root.quit()

    simulation_thread = threading.Thread(target=run_simulation, daemon=True)
    simulation_thread.start()

    print("👀 Regardez la fenêtre d'extraction en action !")
    print("⏱️  Simulation: Téléchargement → Extraction → Terminé")
    print("🎯 Fenêtre identique au launcher principal")

    # Lancer la boucle principale
    root.mainloop()

    print("\n✅ Test terminé !")
    print("💡 Cette fenêtre est identique à celle du launcher")
    print("🔧 Vous pouvez la modifier pour tester de nouveaux designs")

if __name__ == "__main__":
    main()
