#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Test du système 100% Firebase (sans stockage local)

import sys
import os

# Ajouter le chemin du projet pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import pyrebase4 as pyrebase
    print("✅ pyrebase4 importé avec succès")
except ImportError:
    print("❌ pyrebase4 non trouvé - utilisez: .venv\\Scripts\\python.exe test_firebase_only.py")
    exit(1)

# Configuration Firebase (identique au launcher)
firebaseConfig = {
    "apiKey": "AIzaSyD0Dlx2H_HTLIBPnjyWiIqTsYxSQNzutUg",
    "authDomain": "cracken-auth.firebaseapp.com",
    "databaseURL": "https://cracken-auth-default-rtdb.europe-west1.firebasedatabase.app/",
    "projectId": "cracken-auth",
    "storageBucket": "cracken-auth.firebasestorage.app",
    "messagingSenderId": "190741178779",
    "appId": "1:190741178779:web:fdc71a275be566b3db993e"
}

def test_firebase_only_system():
    """Test du système 100% Firebase"""
    print("🔥 TEST SYSTÈME 100% FIREBASE")
    print("=" * 60)
    
    try:
        print("\n1️⃣ Initialisation Firebase...")
        firebase = pyrebase.initialize_app(firebaseConfig)
        db = firebase.database()
        print("✅ Firebase initialisé avec succès")
        
        # Test 1: Simulation d'un nouvel utilisateur
        print("\n2️⃣ Test nouvel utilisateur...")
        test_email = "<EMAIL>"
        test_nickname = "NouveauUser"
        user_key = test_email.replace('.', '_').replace('@', '_at_')
        
        # Vérifier qu'il n'existe pas
        existing_user = db.child("users").child(user_key).get()
        if existing_user.val():
            print("🧹 Nettoyage utilisateur test existant...")
            db.child("users").child(user_key).remove()
        
        # Créer le nouvel utilisateur (comme le ferait le launcher)
        user_data = {
            'email': test_email,
            'nickname': test_nickname,
            'created_at': '2024-01-01',
            'last_updated': '2024-01-01',
            'storage_type': 'firebase_only'
        }
        
        db.child("users").child(user_key).set(user_data)
        print(f"✅ Utilisateur créé: {test_email} → {test_nickname}")
        
        # Test 2: Récupération du pseudo (comme le ferait le launcher)
        print("\n3️⃣ Test récupération pseudo...")
        retrieved_user = db.child("users").child(user_key).get()
        if retrieved_user.val():
            retrieved_nickname = retrieved_user.val().get('nickname')
            if retrieved_nickname == test_nickname:
                print(f"✅ Pseudo récupéré avec succès: {retrieved_nickname}")
            else:
                print(f"❌ Pseudo incorrect: attendu {test_nickname}, reçu {retrieved_nickname}")
        else:
            print("❌ Utilisateur non trouvé")
        
        # Test 3: Simulation connexion sur autre appareil
        print("\n4️⃣ Test simulation autre appareil...")
        # Sur un autre appareil, on chercherait le pseudo avec l'email
        other_device_user = db.child("users").child(user_key).get()
        if other_device_user.val():
            nickname_on_other_device = other_device_user.val().get('nickname')
            print(f"✅ Pseudo récupéré sur 'autre appareil': {nickname_on_other_device}")
        else:
            print("❌ Impossible de récupérer le pseudo sur autre appareil")
        
        # Test 4: Vérification qu'il n'y a pas de stockage local
        print("\n5️⃣ Test absence de stockage local...")
        local_file = "launcher_data.json"
        if os.path.exists(local_file):
            import json
            with open(local_file, 'r') as f:
                data = json.load(f)
            
            # Vérifier qu'il n'y a pas de données d'auth locales
            if 'user_auth' in data:
                print("⚠️  ATTENTION: Données d'auth locales détectées (à supprimer)")
            else:
                print("✅ Pas de données d'auth locales")
            
            if 'user_nicknames' in data:
                print("⚠️  ATTENTION: Pseudos locaux détectés (à supprimer)")
            else:
                print("✅ Pas de pseudos locaux")
        else:
            print("✅ Aucun fichier local de données d'auth")
        
        # Test 5: Liste de tous les utilisateurs
        print("\n6️⃣ Liste des utilisateurs Firebase...")
        all_users = db.child("users").get()
        if all_users.val():
            user_count = len(all_users.val())
            print(f"✅ {user_count} utilisateur(s) dans Firebase:")
            for key, user_data in all_users.val().items():
                if isinstance(user_data, dict):
                    email = user_data.get('email', 'N/A')
                    nickname = user_data.get('nickname', 'N/A')
                    storage_type = user_data.get('storage_type', 'legacy')
                    print(f"   - {email} → {nickname} ({storage_type})")
        else:
            print("❌ Aucun utilisateur trouvé")
        
        # Nettoyage
        print("\n7️⃣ Nettoyage...")
        db.child("users").child(user_key).remove()
        print("✅ Utilisateur test supprimé")
        
        print("\n🎉 SYSTÈME 100% FIREBASE OPÉRATIONNEL!")
        print("✅ Plus de stockage local des comptes")
        print("✅ Synchronisation automatique entre appareils")
        print("✅ Sécurité maximale")
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_firebase_only_system()
    if success:
        print("\n🚀 PRÊT POUR LE LAUNCHER 100% FIREBASE!")
        print("Utilisez: .venv\\Scripts\\python.exe \"launcher v2.py\"")
    else:
        print("\n⚠️  Problème détecté")
    
    input("\nAppuyez sur Entrée pour continuer...")
