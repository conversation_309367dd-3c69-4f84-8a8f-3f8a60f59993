#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier l'ajout du type de jeu dans le Cracken Launcher
"""

import json
import os

def test_game_type_feature():
    """Test l'ajout du type de jeu dans la base de données et l'interface"""
    
    print("🧪 Test de la fonctionnalité 'Type de jeu'")
    print("=" * 50)
    
    # 1. Test de la structure de données pour un jeu du store
    print("\n1️⃣ Test structure de données du store:")
    
    # Exemple de données d'un jeu du store avec le nouveau champ 'type'
    store_game_example = {
        "game_001": {
            "official_name": "Super Adventure Game",
            "description": "Un jeu d'aventure épique avec des graphismes magnifiques.",
            "type": "Aventure",  # ← Nouveau champ
            "image_url": "https://example.com/image.jpg",
            "exe_name": "game.exe",
            "torrent_url": "https://example.com/game.torrent"
        },
        "game_002": {
            "official_name": "Racing Championship",
            "description": "Course de voitures ultra-réaliste.",
            "type": "Course",  # ← Nouveau champ
            "image_url": "https://example.com/racing.jpg",
            "exe_name": "racing.exe",
            "torrent_url": "https://example.com/racing.torrent"
        },
        "game_003": {
            "official_name": "Mystery Puzzle",
            "description": "Résolvez des énigmes complexes.",
            "type": "Puzzle",  # ← Nouveau champ
            "image_url": "https://example.com/puzzle.jpg",
            "exe_name": "puzzle.exe",
            "torrent_url": "https://example.com/puzzle.torrent"
        }
    }
    
    for game_id, game_data in store_game_example.items():
        print(f"   {game_id}:")
        print(f"     - Nom: {game_data['official_name']}")
        print(f"     - Type: {game_data['type']} ✅")
        print(f"     - Description: {game_data['description'][:50]}...")
    
    # 2. Test de la structure de données pour un jeu de la bibliothèque
    print("\n2️⃣ Test structure de données de la bibliothèque:")
    
    # Exemple de données d'un jeu dans la bibliothèque avec le nouveau champ 'type'
    library_game_example = {
        "name": "Super Adventure Game",
        "path": "C:/Games/Adventure/game.exe",
        "image_path": "C:/Users/<USER>/CrackenLauncher/images/game_001.jpg",
        "description": "Un jeu d'aventure épique avec des graphismes magnifiques.",
        "type": "Aventure",  # ← Nouveau champ ajouté lors de l'import du store
        "playtime": 7200,  # 2 heures
        "game_id": "game_001"
    }
    
    print(f"   Jeu de la bibliothèque:")
    print(f"     - Nom: {library_game_example['name']}")
    print(f"     - Type: {library_game_example['type']} ✅")
    print(f"     - Temps de jeu: {library_game_example['playtime']//3600}h")
    
    # 3. Test de compatibilité avec les anciens jeux (sans type)
    print("\n3️⃣ Test compatibilité avec anciens jeux:")
    
    old_game_example = {
        "name": "Ancien Jeu",
        "path": "C:/Games/Old/old.exe",
        "image_path": "C:/Users/<USER>/CrackenLauncher/images/old.jpg",
        "description": "Un ancien jeu sans type défini.",
        # Pas de champ 'type' → doit afficher "Type non spécifié"
        "playtime": 3600,
        "game_id": "old_game"
    }
    
    # Simulation de la logique du launcher
    game_type = old_game_example.get('type', 'Type non spécifié')
    print(f"   Ancien jeu sans type:")
    print(f"     - Nom: {old_game_example['name']}")
    print(f"     - Type: {game_type} ✅ (fallback)")
    
    # 4. Test des types de jeux courants
    print("\n4️⃣ Exemples de types de jeux:")
    
    common_game_types = [
        "Action", "Aventure", "RPG", "Stratégie", "Simulation", 
        "Course", "Sport", "Puzzle", "Plateforme", "FPS",
        "MMORPG", "Indie", "Horreur", "Arcade", "Casual"
    ]
    
    for i, game_type in enumerate(common_game_types, 1):
        print(f"   {i:2d}. {game_type}")
    
    # 5. Test de l'affichage dans l'interface
    print("\n5️⃣ Test affichage interface:")
    print("   Store:")
    print("     [Image] Nom du jeu")
    print("             Type: Aventure ← Nouvelle ligne")
    print("             Description du jeu...")
    print("             [Ajouter] [Télécharger]")
    print()
    print("   Bibliothèque (page détail):")
    print("     [Image] Nom du jeu")
    print("             Type: Aventure ← Nouvelle ligne")
    print("             Temps de jeu: 2h 30m")
    print("             Description...")
    print("             [Lancer le jeu]")
    
    print("\n✅ Tous les tests sont passés!")
    print("🎮 La fonctionnalité 'Type de jeu' est prête!")
    
    return True

def create_sample_database():
    """Crée un exemple de base de données avec des types de jeux"""
    
    sample_db = {
        "minecraft": {
            "official_name": "Minecraft",
            "description": "Jeu de construction et d'aventure en monde ouvert.",
            "type": "Sandbox",
            "image_url": "https://example.com/minecraft.jpg",
            "exe_name": "minecraft.exe",
            "torrent_url": "https://example.com/minecraft.torrent"
        },
        "gta5": {
            "official_name": "Grand Theft Auto V",
            "description": "Jeu d'action en monde ouvert dans la ville de Los Santos.",
            "type": "Action",
            "image_url": "https://example.com/gta5.jpg",
            "exe_name": "GTA5.exe",
            "torrent_url": "https://example.com/gta5.torrent"
        },
        "fifa23": {
            "official_name": "FIFA 23",
            "description": "Simulation de football avec les équipes officielles.",
            "type": "Sport",
            "image_url": "https://example.com/fifa23.jpg",
            "exe_name": "FIFA23.exe",
            "torrent_url": "https://example.com/fifa23.torrent"
        },
        "cyberpunk": {
            "official_name": "Cyberpunk 2077",
            "description": "RPG futuriste dans la ville de Night City.",
            "type": "RPG",
            "image_url": "https://example.com/cyberpunk.jpg",
            "exe_name": "Cyberpunk2077.exe",
            "torrent_url": "https://example.com/cyberpunk.torrent"
        }
    }
    
    print("\n📝 Exemple de base de données avec types:")
    print(json.dumps(sample_db, indent=2, ensure_ascii=False))
    
    return sample_db

if __name__ == "__main__":
    print("🚀 Cracken Launcher - Test Type de Jeu")
    print("=====================================")
    
    # Exécuter les tests
    test_game_type_feature()
    
    # Créer un exemple de base de données
    create_sample_database()
    
    print("\n" + "="*50)
    print("✨ Modifications apportées au launcher:")
    print("   1. Ajout du champ 'type' dans l'affichage du store")
    print("   2. Sauvegarde du type lors de l'ajout à la bibliothèque")
    print("   3. Affichage du type dans la page détail des jeux")
    print("   4. Compatibilité avec les anciens jeux (fallback)")
    print("\n🎯 Prêt pour utilisation!")
    
    input("\nAppuyez sur Entrée pour continuer...")
