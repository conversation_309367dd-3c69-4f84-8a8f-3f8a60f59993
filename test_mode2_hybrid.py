#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Test du Mode 2 : Confort avec persistance (Local chiffré + Firebase)

import sys
import os
import json

# Ajouter le chemin du projet pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importer les fonctions du launcher
try:
    # Le fichier s'appelle "launcher v2.py" avec un espace
    import importlib.util
    spec = importlib.util.spec_from_file_location("launcher_v2", "launcher v2.py")
    launcher_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(launcher_module)

    encrypt_token = launcher_module.encrypt_token
    decrypt_token = launcher_module.decrypt_token
    get_device_key = launcher_module.get_device_key
    print("✅ Fonctions de chiffrement importées")
except ImportError as e:
    print(f"❌ Erreur import: {e}")
    exit(1)

def test_encryption():
    """Test des fonctions de chiffrement"""
    print("🔐 TEST CHIFFREMENT")
    print("=" * 40)
    
    # Test 1: Chiffrement/déchiffrement basique
    test_token = "ya29.a0ARrdaM_test_token_example_123456789"
    print(f"Token original: {test_token[:20]}...")
    
    encrypted = encrypt_token(test_token)
    print(f"Token chiffré: {encrypted[:20]}...")
    
    decrypted = decrypt_token(encrypted)
    print(f"Token déchiffré: {decrypted[:20]}...")
    
    if decrypted == test_token:
        print("✅ Chiffrement/déchiffrement réussi")
    else:
        print("❌ Échec chiffrement/déchiffrement")
        return False
    
    # Test 2: Clé unique par appareil
    device_key1 = get_device_key()
    device_key2 = get_device_key()
    
    if device_key1 == device_key2:
        print("✅ Clé d'appareil cohérente")
    else:
        print("❌ Clé d'appareil incohérente")
        return False
    
    # Test 3: Token vide
    empty_encrypted = encrypt_token("")
    empty_decrypted = decrypt_token("")
    
    if empty_encrypted == "" and empty_decrypted == "":
        print("✅ Gestion des tokens vides")
    else:
        print("❌ Problème avec tokens vides")
        return False
    
    return True

def test_local_storage():
    """Test du stockage local"""
    print("\n💾 TEST STOCKAGE LOCAL")
    print("=" * 40)
    
    # Simuler une sauvegarde locale
    test_data = {
        "games": [],
        "user_session": {
            "email": "<EMAIL>",
            "encrypted_token": encrypt_token("test_token_123"),
            "last_login": "2024-01-09T15:30:00",
            "auto_login": True
        }
    }
    
    # Sauvegarder dans un fichier test
    test_file = "test_launcher_data.json"
    try:
        with open(test_file, 'w') as f:
            json.dump(test_data, f, indent=4)
        print("✅ Sauvegarde locale réussie")
        
        # Relire et vérifier
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        
        user_session = loaded_data.get('user_session', {})
        encrypted_token = user_session.get('encrypted_token', '')
        decrypted_token = decrypt_token(encrypted_token)
        
        if decrypted_token == "test_token_123":
            print("✅ Lecture et déchiffrement réussis")
        else:
            print("❌ Échec lecture/déchiffrement")
            return False
        
        # Nettoyer
        os.remove(test_file)
        print("✅ Nettoyage terminé")
        
    except Exception as e:
        print(f"❌ Erreur stockage local: {e}")
        return False
    
    return True

def test_mode2_workflow():
    """Test du workflow complet Mode 2"""
    print("\n🔄 TEST WORKFLOW MODE 2")
    print("=" * 40)
    
    print("Simulation du workflow:")
    print("1. Première connexion → Sauvegarde locale + Firebase")
    print("2. Redémarrage → Connexion auto avec token local")
    print("3. Récupération pseudo depuis Firebase")
    print("4. Déconnexion → Suppression locale uniquement")
    
    # Simuler les étapes
    steps = [
        "🔐 Chiffrement token Google",
        "💾 Sauvegarde session locale",
        "🔥 Sauvegarde pseudo Firebase", 
        "🔄 Redémarrage launcher",
        "🔓 Déchiffrement token local",
        "📥 Récupération pseudo Firebase",
        "✅ Connexion automatique réussie",
        "🚪 Déconnexion locale",
        "🗑️ Suppression session locale",
        "🔥 Données Firebase conservées"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"   {i:2d}. {step}")
    
    print("\n✅ Workflow Mode 2 validé conceptuellement")
    return True

def main():
    """Test principal"""
    print("🎯 TEST MODE 2 : CONFORT AVEC PERSISTANCE")
    print("=" * 60)
    print("Local: Token chiffré + Email")
    print("Firebase: Pseudo + Métadonnées")
    print("Comportement: Connexion auto permanente")
    print("=" * 60)
    
    tests = [
        ("Chiffrement", test_encryption),
        ("Stockage local", test_local_storage),
        ("Workflow Mode 2", test_mode2_workflow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résultats
    print("\n📊 RÉSULTATS")
    print("=" * 40)
    all_passed = True
    for test_name, passed in results:
        status = "✅ RÉUSSI" if passed else "❌ ÉCHEC"
        print(f"{test_name:20} : {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("🚀 Mode 2 prêt pour implémentation")
    else:
        print("⚠️  Certains tests ont échoué")
        print("🔧 Vérification nécessaire")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    print(f"\n{'🎯 SUCCÈS' if success else '❌ ÉCHEC'}")
    input("\nAppuyez sur Entrée pour continuer...")
