#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour la détection du type exact "Online-Fix.Me"
"""

import os
import tempfile
import json

def is_online_fix_game(game_data):
    """Vérifie si un jeu est un jeu online-fix basé sur le type dans la base de données"""
    game_type = game_data.get('type', '')
    
    # Seul type supporté pour l'extraction automatique
    return game_type == 'Online-Fix.Me'

def test_online_fix_me_detection():
    """Test la détection du type exact Online-Fix.Me"""
    
    print("🧪 Test de détection du type 'Online-Fix.Me'")
    print("=" * 60)
    
    # Tests de détection avec différents types
    test_games = [
        {
            "official_name": "Call of Duty Modern Warfare",
            "type": "Online-Fix.Me",  # ← Type exact
            "expected": True,
            "description": "Type exact (extraction attendue)"
        },
        {
            "official_name": "FIFA 23",
            "type": "online-fix.me",  # ← Casse différente
            "expected": False,
            "description": "Casse différente (pas d'extraction)"
        },
        {
            "official_name": "GTA V",
            "type": "Online-Fix",  # ← Sans .Me
            "expected": False,
            "description": "Sans .Me (pas d'extraction)"
        },
        {
            "official_name": "Minecraft",
            "type": "Sandbox",  # ← Type normal
            "expected": False,
            "description": "Type normal (pas d'extraction)"
        },
        {
            "official_name": "The Witcher 3",
            "type": "RPG",  # ← Type normal
            "expected": False,
            "description": "Type normal (pas d'extraction)"
        },
        {
            "official_name": "Counter-Strike",
            "type": "Online-Fix.Me",  # ← Type exact
            "expected": True,
            "description": "Type exact (extraction attendue)"
        }
    ]
    
    print("Tests de détection:")
    correct_detections = 0
    
    for i, game in enumerate(test_games, 1):
        result = is_online_fix_game(game)
        status = "✅" if result == game["expected"] else "❌"
        action = "EXTRAIRE" if result else "IGNORER"
        
        if result == game["expected"]:
            correct_detections += 1
        
        print(f"  {i}. {game['official_name']}")
        print(f"     Type: '{game['type']}'")
        print(f"     {game['description']}")
        print(f"     Détection: {result} → {action} {status}")
        print()
    
    print(f"📊 Résultat: {correct_detections}/{len(test_games)} détections correctes")
    
    if correct_detections == len(test_games):
        print("✅ TOUS LES TESTS RÉUSSIS!")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ!")
    
    return correct_detections == len(test_games)

def create_sample_database_online_fix_me():
    """Crée un exemple de base de données avec le type Online-Fix.Me"""
    
    print("\n📝 EXEMPLE DE BASE DE DONNÉES AVEC TYPE 'Online-Fix.Me'")
    print("=" * 60)
    
    sample_db = {
        "cod_mw": {
            "official_name": "Call of Duty Modern Warfare",
            "description": "Jeu de tir multijoueur avec fix online",
            "type": "Online-Fix.Me",  # ← Type exact pour extraction
            "image_url": "https://example.com/cod.jpg",
            "exe_name": "ModernWarfare.exe",
            "torrent_url": "https://example.com/cod.torrent"
        },
        "fifa23": {
            "official_name": "FIFA 23",
            "description": "Simulation de football avec fix multijoueur",
            "type": "Online-Fix.Me",  # ← Type exact pour extraction
            "image_url": "https://example.com/fifa.jpg",
            "exe_name": "FIFA23.exe",
            "torrent_url": "https://example.com/fifa.torrent"
        },
        "gta5": {
            "official_name": "Grand Theft Auto V",
            "description": "Jeu d'action en monde ouvert avec fix online",
            "type": "Online-Fix.Me",  # ← Type exact pour extraction
            "image_url": "https://example.com/gta5.jpg",
            "exe_name": "GTA5.exe",
            "torrent_url": "https://example.com/gta5.torrent"
        },
        "witcher3": {
            "official_name": "The Witcher 3",
            "description": "RPG solo épique",
            "type": "RPG",  # ← Type normal (pas d'extraction)
            "image_url": "https://example.com/witcher.jpg",
            "exe_name": "witcher3.exe",
            "torrent_url": "https://example.com/witcher.torrent"
        },
        "minecraft": {
            "official_name": "Minecraft",
            "description": "Jeu de construction en monde ouvert",
            "type": "Sandbox",  # ← Type normal (pas d'extraction)
            "image_url": "https://example.com/minecraft.jpg",
            "exe_name": "minecraft.exe",
            "torrent_url": "https://example.com/minecraft.torrent"
        }
    }
    
    print(json.dumps(sample_db, indent=2, ensure_ascii=False))
    
    # Analyser la base de données
    online_fix_games = [game for game in sample_db.values() if game['type'] == 'Online-Fix.Me']
    normal_games = [game for game in sample_db.values() if game['type'] != 'Online-Fix.Me']
    
    print(f"\n📊 ANALYSE DE LA BASE DE DONNÉES:")
    print(f"   🔓 Jeux Online-Fix.Me: {len(online_fix_games)}")
    print(f"   🎮 Jeux normaux: {len(normal_games)}")
    
    print(f"\n🔓 JEUX AVEC EXTRACTION AUTOMATIQUE:")
    for game in online_fix_games:
        print(f"   - {game['official_name']}")
    
    print(f"\n🎮 JEUX SANS EXTRACTION:")
    for game in normal_games:
        print(f"   - {game['official_name']} (Type: {game['type']})")
    
    return sample_db

def test_extraction_workflow_simplified():
    """Test le workflow simplifié avec le type exact"""
    
    print(f"\n⚙️  WORKFLOW D'EXTRACTION SIMPLIFIÉ")
    print("=" * 60)
    
    print("Étapes du processus:")
    print("  1. ✅ Téléchargement terminé avec succès")
    print("  2. 🔍 Vérification: game_data['type'] == 'Online-Fix.Me'")
    print("  3. 📁 Si OUI → Recherche des archives RAR")
    print("  4. 🎯 Sélection de l'archive principale (part1)")
    print("  5. 🔓 Extraction avec mot de passe 'online-fix.me'")
    print("  6. ✅ Mise à jour du statut de progression")
    print("  7. 🎉 'Téléchargement et extraction terminés !'")
    
    print(f"\n🎯 CONFIGURATION REQUISE:")
    print(f"   - Type exact dans la DB: 'Online-Fix.Me'")
    print(f"   - Mot de passe d'extraction: 'online-fix.me'")
    print(f"   - Module rarfile installé")
    print(f"   - WinRAR ou unrar sur le système")
    
    print(f"\n✅ AVANTAGES DE LA SIMPLIFICATION:")
    print(f"   - Détection ultra-précise (un seul type)")
    print(f"   - Pas de confusion avec d'autres types")
    print(f"   - Configuration simple dans la DB")
    print(f"   - Maintenance facile")

def simulate_launcher_behavior():
    """Simule le comportement du launcher avec différents jeux"""
    
    print(f"\n🎮 SIMULATION DU COMPORTEMENT DU LAUNCHER")
    print("=" * 60)
    
    # Jeux de test
    test_scenarios = [
        {
            "name": "Call of Duty MW",
            "type": "Online-Fix.Me",
            "has_rar": True,
            "expected_behavior": "Extraction automatique"
        },
        {
            "name": "FIFA 23",
            "type": "Online-Fix.Me",
            "has_rar": True,
            "expected_behavior": "Extraction automatique"
        },
        {
            "name": "The Witcher 3",
            "type": "RPG",
            "has_rar": False,
            "expected_behavior": "Aucune extraction"
        },
        {
            "name": "Minecraft",
            "type": "Sandbox",
            "has_rar": False,
            "expected_behavior": "Aucune extraction"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📋 Scénario: {scenario['name']}")
        print(f"   Type: '{scenario['type']}'")
        
        # Simuler la détection
        game_data = {"type": scenario["type"]}
        is_online_fix = is_online_fix_game(game_data)
        
        if is_online_fix:
            print(f"   🔍 Détection: ✅ Online-Fix.Me détecté")
            if scenario["has_rar"]:
                print(f"   📁 Archives RAR trouvées")
                print(f"   🔓 Extraction en cours...")
                print(f"   ✅ Extraction terminée !")
                print(f"   📢 Statut: 'Téléchargement et extraction terminés !'")
            else:
                print(f"   ❌ Aucune archive RAR trouvée")
                print(f"   📢 Statut: 'Téléchargement terminé - Aucune archive'")
        else:
            print(f"   🔍 Détection: ❌ Type normal")
            print(f"   ⏭️  Extraction ignorée")
            print(f"   📢 Statut: 'Téléchargement terminé'")
        
        actual_behavior = "Extraction automatique" if is_online_fix else "Aucune extraction"
        status = "✅" if actual_behavior == scenario["expected_behavior"] else "❌"
        print(f"   🎯 Comportement: {actual_behavior} {status}")

if __name__ == "__main__":
    print("🚀 CRACKEN LAUNCHER - TEST TYPE 'Online-Fix.Me'")
    print("=" * 70)
    print("Test de la détection du type exact 'Online-Fix.Me'")
    print("Seul ce type déclenchera l'extraction automatique")
    print()
    
    # Lancer tous les tests
    success = test_online_fix_me_detection()
    create_sample_database_online_fix_me()
    test_extraction_workflow_simplified()
    simulate_launcher_behavior()
    
    print("\n" + "="*70)
    print("✨ CONFIGURATION FINALE:")
    print("   🎯 Type exact requis: 'Online-Fix.Me'")
    print("   🔑 Mot de passe: 'online-fix.me'")
    print("   📁 Archives: *.rar (priorité part1)")
    print("   ⚙️  Extraction: Automatique après téléchargement")
    print()
    
    if success:
        print("✅ TOUS LES TESTS RÉUSSIS!")
        print("🎮 Prêt pour utilisation avec le type 'Online-Fix.Me'")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ!")
        print("🔧 Vérifiez la configuration")
    
    print(f"\n🎯 POUR UTILISER CETTE FONCTIONNALITÉ:")
    print(f"   1. Mettre 'Online-Fix.Me' dans le champ 'type' de la DB")
    print(f"   2. Le launcher détectera et extraira automatiquement")
    print(f"   3. Mot de passe utilisé: 'online-fix.me'")
    
    input("\nAppuyez sur Entrée pour continuer...")
