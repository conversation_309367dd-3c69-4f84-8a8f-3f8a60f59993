#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier la détection avec le type correct 'Online-Fx.Me'
"""

def is_online_fix_game(game_data):
    """Vérifie si un jeu est un jeu online-fix basé sur le type dans la base de données"""
    game_type = game_data.get('type', '')
    
    # Seul type supporté pour l'extraction automatique
    return game_type == 'Online-Fx.Me'

def test_content_warning_detection():
    """Test la détection pour Content Warning avec le bon type"""
    
    print("🎮 TEST DE DÉTECTION - Content Warning")
    print("=" * 60)
    
    # Test avec le type exact de la DB
    content_warning_data = {
        "official_name": "Content Warning",
        "type": "Online-Fx.Me",  # Type exact de la DB
        "description": "Filmez vos amis en train de faire des choses terrifiantes..."
    }
    
    print("Données du jeu Content Warning:")
    print(f"   Nom: {content_warning_data['official_name']}")
    print(f"   Type: '{content_warning_data['type']}'")
    print(f"   Description: {content_warning_data['description'][:50]}...")
    
    # Tester la détection
    detected = is_online_fix_game(content_warning_data)
    
    print(f"\n🔍 Résultat de la détection:")
    if detected:
        print("   ✅ DÉTECTÉ comme jeu online-fix")
        print("   🔓 L'extraction automatique sera déclenchée")
        print("   📦 Recherche des archives RAR...")
        print("   🔑 Extraction avec mot de passe 'online-fix.me'")
    else:
        print("   ❌ NON DÉTECTÉ comme jeu online-fix")
        print("   ⏭️  Aucune extraction automatique")
    
    return detected

def test_various_types():
    """Test avec différents types pour comparaison"""
    
    print(f"\n🧪 TEST AVEC DIFFÉRENTS TYPES")
    print("=" * 60)
    
    test_cases = [
        {"type": "Online-Fx.Me", "expected": True, "description": "Type exact de la DB"},
        {"type": "Online-Fix.Me", "expected": False, "description": "Ancien type (avec Fix)"},
        {"type": "online-fx.me", "expected": False, "description": "Minuscules"},
        {"type": "Online-Fx", "expected": False, "description": "Sans .Me"},
        {"type": "Horror", "expected": False, "description": "Type normal"},
        {"type": "Action", "expected": False, "description": "Type normal"},
    ]
    
    print("Comparaison des types:")
    correct_detections = 0
    
    for test_case in test_cases:
        game_data = {"type": test_case["type"]}
        detected = is_online_fix_game(game_data)
        expected = test_case["expected"]
        
        if detected == expected:
            status = "✅"
            correct_detections += 1
        else:
            status = "❌"
        
        action = "EXTRAIRE" if detected else "IGNORER"
        print(f"   '{test_case['type']}' → {action} {status} ({test_case['description']})")
    
    print(f"\n📊 Résultat: {correct_detections}/{len(test_cases)} détections correctes")
    return correct_detections == len(test_cases)

def simulate_download_workflow():
    """Simule le workflow de téléchargement avec Content Warning"""
    
    print(f"\n🔄 SIMULATION DU WORKFLOW DE TÉLÉCHARGEMENT")
    print("=" * 60)
    
    print("Étapes du processus:")
    print("1. ✅ Utilisateur télécharge Content Warning")
    print("2. ✅ Téléchargement terminé avec succès")
    print("3. 🔍 Vérification du type: 'Online-Fx.Me'")
    
    # Simuler la détection
    game_data = {"type": "Online-Fx.Me"}
    is_online_fix = is_online_fix_game(game_data)
    
    if is_online_fix:
        print("4. ✅ Type 'Online-Fx.Me' détecté !")
        print("5. 📁 Recherche des archives RAR dans le dossier...")
        print("6. 🎯 Archive trouvée: Content.Warning.v1.17.b-OFME.rar")
        print("7. 🔓 Extraction avec mot de passe 'online-fix.me'...")
        print("8. ✅ Extraction terminée !")
        print("9. 📢 Statut: 'Téléchargement et extraction terminés !'")
    else:
        print("4. ❌ Type non reconnu")
        print("5. ⏭️  Extraction ignorée")
        print("6. 📢 Statut: 'Téléchargement terminé'")
    
    return is_online_fix

def main():
    """Fonction principale"""
    
    print("🚀 CRACKEN LAUNCHER - TEST TYPE 'Online-Fx.Me'")
    print("=" * 70)
    print("Test avec le type exact trouvé dans la base de données")
    print()
    
    # Lancer tous les tests
    content_warning_detected = test_content_warning_detection()
    all_types_correct = test_various_types()
    workflow_success = simulate_download_workflow()
    
    print("\n" + "="*70)
    print("📋 RÉSUMÉ DES TESTS:")
    print(f"   Content Warning détecté: {'✅' if content_warning_detected else '❌'}")
    print(f"   Tous les types corrects: {'✅' if all_types_correct else '❌'}")
    print(f"   Workflow simulé: {'✅' if workflow_success else '❌'}")
    
    if content_warning_detected and all_types_correct and workflow_success:
        print("\n✅ TOUS LES TESTS RÉUSSIS!")
        print("🎮 Content Warning sera maintenant extrait automatiquement")
        print("🔧 Le launcher a été corrigé pour le type 'Online-Fx.Me'")
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez la configuration")
    
    print(f"\n🎯 TYPE EXACT REQUIS DANS LA DB:")
    print(f"   'Online-Fx.Me' (avec Fx, pas Fix)")
    
    print(f"\n🔑 INFORMATIONS:")
    print(f"   - Type détecté: 'Online-Fx.Me'")
    print(f"   - Mot de passe: 'online-fix.me'")
    print(f"   - Archive: Content.Warning.v1.17.b-OFME.rar")
    
    return content_warning_detected

if __name__ == "__main__":
    success = main()
    input(f"\n{'✅ Correction appliquée' if success else '❌ Problème détecté'} - Appuyez sur Entrée pour continuer...")
