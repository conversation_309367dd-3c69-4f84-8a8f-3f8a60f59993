#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier la progression de l'extraction
"""

import tkinter as tk
import time
import threading

# Couleurs exactes du launcher
BACKGROUND_COLOR = "#0B1317"
TEXT_COLOR = "#FFFFFF"

def create_test_window():
    """Crée une fenêtre de test identique au launcher"""
    
    root = tk.Tk()
    root.withdraw()
    
    progress_win = tk.Toplevel(root)
    progress_win.title("Test Progression Extraction")
    progress_win.configure(bg=BACKGROUND_COLOR)
    progress_win.resizable(False, False)
    progress_win.geometry("600x280")

    # Centrer la fenêtre
    progress_win.update_idletasks()
    width = progress_win.winfo_width()
    height = progress_win.winfo_height()
    x = (progress_win.winfo_screenwidth() // 2) - (width // 2)
    y = (progress_win.winfo_screenheight() // 2) - (height // 2)
    progress_win.geometry(f'{width}x{height}+{x}+{y}')

    # Titre du jeu
    title = tk.Label(progress_win, text="Content Warning", font=("Helvetica", 18, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title.pack(pady=(18, 10))

    # Barre de progression
    progress_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    progress_frame.pack(pady=(0, 10))
    progress_var = tk.DoubleVar(value=0)
    progress_bar = tk.Canvas(progress_frame, width=480, height=22, bg="#232728", highlightthickness=0)
    bar_rect = progress_bar.create_rectangle(0, 0, 0, 22, fill="#4be08a", width=0)
    progress_bar.pack()

    # Frame pour % et vitesse
    info_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    info_frame.pack(pady=(0, 2))
    percent_label = tk.Label(info_frame, text="0%", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    percent_label.pack(side="left", padx=(0, 10))
    speed_label = tk.Label(info_frame, text="0.0 Mo/s", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    speed_label.pack(side="left")

    # Affichage des Go
    size_label = tk.Label(progress_win, text="0.00/0.00 Go", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    size_label.pack(pady=(5, 2))

    # État
    state_label = tk.Label(progress_win, text="Téléchargement en cours...", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    state_label.pack(pady=(0, 15))

    # Bouton Arrêter
    def stop_callback():
        progress_win.destroy()
        root.quit()
    
    stop_btn = tk.Button(progress_win, text="Arrêter", font=("Helvetica", 12, "bold"), bg="#e74c3c", fg="#fff", relief="flat", padx=40, pady=3, command=stop_callback)
    stop_btn.pack(pady=(5, 0))

    # Fonction update_progress (identique au launcher)
    def update_progress(percent, state, speed=0.0, downloaded_gb=0.0, total_gb=0.0, extraction_info=""):
        if progress_win.winfo_exists():
            percent = max(0, min(100, percent))
            progress_var.set(percent)
            progress_bar.coords(bar_rect, 0, 0, 4.8 * percent, 22)
            percent_label.config(text=f"{int(percent)}%")

            # Si on est en mode extraction
            if extraction_info:
                # Garder la vitesse vide (pas de "Extraction" à côté du %)
                speed_label.config(text="")
                # Cacher la ligne des Go
                size_label.config(text="")
                # Afficher "Extraction en cours..." dans le statut
                state_label.config(text="Extraction en cours...")
            else:
                # Mode téléchargement normal
                speed_label.config(text=f"{speed:.2f} Mo/s")

                # Mettre à jour l'affichage des Go téléchargés/total
                if total_gb > 0:
                    size_label.config(text=f"{downloaded_gb:.2f}/{total_gb:.2f} Go")
                else:
                    if downloaded_gb > 0:
                        estimated_total = downloaded_gb / (percent / 100) if percent > 0 else downloaded_gb
                        size_label.config(text=f"{downloaded_gb:.2f}/{estimated_total:.2f} Go")
                    else:
                        size_label.config(text="0.00/0.00 Go")

                # Afficher le statut normal
                state_label.config(text=state)

            progress_win.update_idletasks()
            
            print(f"🔍 DEBUG: Barre mise à jour - {percent}% - Mode extraction: {'Oui' if extraction_info else 'Non'}")
    
    return root, progress_win, update_progress

def simulate_extraction_with_progression(update_progress_func):
    """Simule une extraction avec progression qui fonctionne"""
    
    print("📥 Phase téléchargement...")
    # Phase téléchargement
    for i in range(0, 101, 10):
        update_progress_func(i, "Téléchargement en cours...", 45.2, i*0.5, 50.0)
        time.sleep(0.2)
    
    print("🔄 Transition vers extraction...")
    time.sleep(1)
    
    # Phase extraction avec progression qui marche
    extraction_steps = [
        (0, "Préparation de l'extraction..."),
        (10, "Recherche des archives..."),
        (20, "Sélection de l'archive principale..."),
        (30, "Ouverture de l'archive..."),
        (40, "Analyse du contenu..."),
        (50, "Extraction des fichiers..."),
        (60, "Extraction en cours..."),
        (70, "Extraction en cours..."),
        (80, "Extraction en cours..."),
        (90, "Finalisation..."),
        (95, "Archive supprimée"),
        (100, "Extraction terminée !")
    ]
    
    print("🔓 Phase extraction avec progression...")
    for percent, message in extraction_steps:
        print(f"📢 Simulation: {message} - {percent}%")
        # Passer extraction_info pour activer le mode extraction
        update_progress_func(percent, message, 0.0, 0.0, 0.0, "extraction_active")
        time.sleep(0.8)
    
    print("✅ Extraction terminée !")
    time.sleep(3)

def main():
    """Fonction principale"""
    
    print("🚀 TEST PROGRESSION EXTRACTION")
    print("=" * 60)
    print("Test de la progression de l'extraction de 0% à 100%")
    print()
    
    # Créer la fenêtre
    root, progress_win, update_progress = create_test_window()
    
    print("✅ Fenêtre créée")
    print("🔄 Démarrage de la simulation...")
    
    # Lancer la simulation dans un thread
    def run_simulation():
        time.sleep(1)
        simulate_extraction_with_progression(update_progress)
        if progress_win.winfo_exists():
            progress_win.destroy()
        root.quit()
    
    simulation_thread = threading.Thread(target=run_simulation, daemon=True)
    simulation_thread.start()
    
    print("👀 Regardez la barre de progression !")
    print("📊 Elle devrait aller de 0% à 100% pendant l'extraction")
    
    # Lancer la boucle principale
    root.mainloop()
    
    print("\n✅ Test terminé !")
    print("💡 Si la barre progresse correctement, le problème est dans les fonctions d'extraction")

if __name__ == "__main__":
    main()
