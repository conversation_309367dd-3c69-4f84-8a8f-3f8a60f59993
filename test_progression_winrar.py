#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour simuler la progression réelle comme WinRAR
"""

import tkinter as tk
import time
import threading

# Couleurs exactes du launcher
BACKGROUND_COLOR = "#0B1317"
TEXT_COLOR = "#FFFFFF"

def create_test_window():
    """Crée une fenêtre de test identique au launcher"""
    
    root = tk.Tk()
    root.withdraw()
    
    progress_win = tk.Toplevel(root)
    progress_win.title("Test Progression WinRAR")
    progress_win.configure(bg=BACKGROUND_COLOR)
    progress_win.resizable(False, False)
    progress_win.geometry("600x280")

    # Centrer la fenêtre
    progress_win.update_idletasks()
    width = progress_win.winfo_width()
    height = progress_win.winfo_height()
    x = (progress_win.winfo_screenwidth() // 2) - (width // 2)
    y = (progress_win.winfo_screenheight() // 2) - (height // 2)
    progress_win.geometry(f'{width}x{height}+{x}+{y}')

    # Titre du jeu
    title = tk.Label(progress_win, text="Content Warning", font=("Helvetica", 18, "bold"), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    title.pack(pady=(18, 10))

    # Barre de progression
    progress_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    progress_frame.pack(pady=(0, 10))
    progress_var = tk.DoubleVar(value=0)
    progress_bar = tk.Canvas(progress_frame, width=480, height=22, bg="#232728", highlightthickness=0)
    bar_rect = progress_bar.create_rectangle(0, 0, 0, 22, fill="#4be08a", width=0)
    progress_bar.pack()

    # Frame pour % et vitesse
    info_frame = tk.Frame(progress_win, bg=BACKGROUND_COLOR)
    info_frame.pack(pady=(0, 2))
    percent_label = tk.Label(info_frame, text="0%", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    percent_label.pack(side="left", padx=(0, 10))
    speed_label = tk.Label(info_frame, text="0.0 Mo/s", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    speed_label.pack(side="left")

    # Affichage des Go
    size_label = tk.Label(progress_win, text="0.00/0.00 Go", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    size_label.pack(pady=(5, 2))

    # État
    state_label = tk.Label(progress_win, text="Téléchargement en cours...", font=("Helvetica", 13), bg=BACKGROUND_COLOR, fg=TEXT_COLOR)
    state_label.pack(pady=(0, 15))

    # Bouton Arrêter
    def stop_callback():
        progress_win.destroy()
        root.quit()
    
    stop_btn = tk.Button(progress_win, text="Arrêter", font=("Helvetica", 12, "bold"), bg="#e74c3c", fg="#fff", relief="flat", padx=40, pady=3, command=stop_callback)
    stop_btn.pack(pady=(5, 0))

    # Fonction update_progress (identique au launcher)
    def update_progress(percent, state, speed=0.0, downloaded_gb=0.0, total_gb=0.0, extraction_info=""):
        if progress_win.winfo_exists():
            percent = max(0, min(100, percent))
            progress_var.set(percent)
            progress_bar.coords(bar_rect, 0, 0, 4.8 * percent, 22)
            percent_label.config(text=f"{int(percent)}%")

            # Si on est en mode extraction
            if extraction_info:
                speed_label.config(text="")
                size_label.config(text="")
                state_label.config(text="Extraction en cours...")
            else:
                speed_label.config(text=f"{speed:.2f} Mo/s")
                if total_gb > 0:
                    size_label.config(text=f"{downloaded_gb:.2f}/{total_gb:.2f} Go")
                else:
                    if downloaded_gb > 0:
                        estimated_total = downloaded_gb / (percent / 100) if percent > 0 else downloaded_gb
                        size_label.config(text=f"{downloaded_gb:.2f}/{estimated_total:.2f} Go")
                    else:
                        size_label.config(text="0.00/0.00 Go")
                state_label.config(text=state)

            progress_win.update_idletasks()
            
            print(f"🔍 DEBUG: Barre mise à jour - {percent}% - Mode extraction: {'Oui' if extraction_info else 'Non'}")
    
    return root, progress_win, update_progress

def simulate_winrar_extraction(update_progress_func):
    """Simule une extraction comme WinRAR - progression fichier par fichier"""
    
    print("📥 Phase téléchargement...")
    # Phase téléchargement rapide
    for i in range(0, 101, 20):
        update_progress_func(i, "Téléchargement en cours...", 45.2, i*0.5, 50.0)
        time.sleep(0.1)
    
    print("🔄 Transition vers extraction...")
    time.sleep(1)
    
    # Simulation de fichiers à extraire (comme Content Warning)
    files_to_extract = [
        "Content Warning.exe",
        "UnityCrashHandler64.exe", 
        "UnityPlayer.dll",
        "Content Warning_Data/app.info",
        "Content Warning_Data/boot.config",
        "Content Warning_Data/globalgamemanagers",
        "Content Warning_Data/globalgamemanagers.assets",
        "Content Warning_Data/level0",
        "Content Warning_Data/level0.resS",
        "Content Warning_Data/level1",
        "Content Warning_Data/level1.resS",
        "Content Warning_Data/sharedassets0.assets",
        "Content Warning_Data/sharedassets0.assets.resS",
        "Content Warning_Data/sharedassets1.assets",
        "Content Warning_Data/sharedassets1.assets.resS",
        "Content Warning_Data/Resources/unity_builtin_extra",
        "Content Warning_Data/Resources/unity default resources",
        "Content Warning_Data/StreamingAssets/aa/catalog.json",
        "Content Warning_Data/StreamingAssets/aa/settings.json",
        "Content Warning_Data/Managed/Assembly-CSharp.dll",
        "Content Warning_Data/Managed/Unity.Timeline.dll",
        "Content Warning_Data/Managed/UnityEngine.dll",
        "Content Warning_Data/Managed/UnityEngine.UI.dll",
        "Content Warning_Data/Plugins/steam_api64.dll",
        "Content Warning_Data/Plugins/CSteamworks.dll"
    ]
    
    print(f"🔓 Extraction de {len(files_to_extract)} fichiers...")
    
    # Extraire fichier par fichier comme WinRAR
    total_files = len(files_to_extract)
    for i, filename in enumerate(files_to_extract):
        # Calculer le pourcentage réel
        progress_percent = int((i / total_files) * 100)
        
        print(f"📄 Extraction {i+1}/{total_files}: {filename}")
        
        # Mettre à jour la barre avec la vraie progression
        update_progress_func(progress_percent, "Extraction en cours...", 0.0, 0.0, 0.0, "extraction_active")
        
        # Temps variable selon la taille du fichier (simulation)
        if filename.endswith('.dll') or filename.endswith('.exe'):
            time.sleep(0.3)  # Fichiers plus gros
        elif 'level' in filename or 'assets' in filename:
            time.sleep(0.5)  # Fichiers très gros
        else:
            time.sleep(0.1)  # Petits fichiers
    
    # Finalisation
    update_progress_func(100, "Extraction en cours...", 0.0, 0.0, 0.0, "extraction_active")
    print("✅ Extraction terminée !")
    time.sleep(2)

def main():
    """Fonction principale"""
    
    print("🚀 TEST PROGRESSION COMME WINRAR")
    print("=" * 60)
    print("Simulation de la progression réelle fichier par fichier")
    print()
    
    # Créer la fenêtre
    root, progress_win, update_progress = create_test_window()
    
    print("✅ Fenêtre créée")
    print("🔄 Démarrage de la simulation WinRAR...")
    
    # Lancer la simulation dans un thread
    def run_simulation():
        time.sleep(1)
        simulate_winrar_extraction(update_progress)
        if progress_win.winfo_exists():
            progress_win.destroy()
        root.quit()
    
    simulation_thread = threading.Thread(target=run_simulation, daemon=True)
    simulation_thread.start()
    
    print("👀 Regardez la progression réelle !")
    print("📊 Chaque fichier fait progresser la barre")
    print("🎯 Comme WinRAR : progression basée sur les fichiers extraits")
    
    # Lancer la boucle principale
    root.mainloop()
    
    print("\n✅ Test terminé !")
    print("💡 C'est maintenant la vraie progression d'extraction !")

if __name__ == "__main__":
    main()
