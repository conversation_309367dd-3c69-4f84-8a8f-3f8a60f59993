#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier la suppression automatique des archives
"""

import os
import tempfile
import rarfile

# Configuration rarfile
if os.path.exists(r"C:\Program Files\WinRAR\UnRAR.exe"):
    rarfile.UNRAR_TOOL = r"C:\Program Files\WinRAR\UnRAR.exe"
    print(f"✅ Configuration rarfile: UnRAR.exe")
elif os.path.exists(r"C:\Program Files\WinRAR\WinRAR.exe"):
    rarfile.UNRAR_TOOL = r"C:\Program Files\WinRAR\WinRAR.exe"
    print(f"⚠️  Configuration rarfile: WinRAR.exe")

def test_extraction_with_deletion():
    """Test l'extraction avec suppression automatique"""
    
    print("🧪 TEST EXTRACTION AVEC SUPPRESSION")
    print("=" * 50)
    
    # Utiliser un dossier temporaire
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Dossier temporaire: {temp_dir}")
        
        # Créer un fichier de test
        test_file = os.path.join(temp_dir, "test_game.exe")
        with open(test_file, 'w') as f:
            f.write("Fake game executable for testing online-fix extraction and deletion")
        
        print("✅ Fichier de test créé")
        
        # Créer une archive RAR avec WinRAR
        rar_file = os.path.join(temp_dir, "test_online_fix.rar")
        
        try:
            import subprocess
            
            # Commande WinRAR pour créer l'archive avec mot de passe
            if os.path.exists(r"C:\Program Files\WinRAR\WinRAR.exe"):
                cmd = [
                    r"C:\Program Files\WinRAR\WinRAR.exe",
                    "a",  # ajouter
                    "-ponline-fix.me",  # mot de passe
                    rar_file,
                    test_file
                ]
                
                print("🔧 Création d'archive RAR avec mot de passe...")
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    print("✅ Archive RAR créée avec succès")
                    print(f"📊 Taille archive: {os.path.getsize(rar_file)} bytes")
                    
                    # Supprimer le fichier original pour tester l'extraction
                    os.remove(test_file)
                    print("🗑️  Fichier original supprimé")
                    
                    # Tester l'extraction avec suppression
                    extract_dir = os.path.join(temp_dir, "extracted")
                    os.makedirs(extract_dir)
                    
                    print("\n🔓 EXTRACTION AVEC SUPPRESSION")
                    print("-" * 30)
                    
                    success = extract_online_fix_archive_with_deletion(rar_file, extract_dir)
                    
                    if success:
                        print("✅ Extraction réussie !")
                        
                        # Vérifier que le fichier a été extrait
                        extracted_file = os.path.join(extract_dir, "test_game.exe")
                        if os.path.exists(extracted_file):
                            print("✅ Fichier extrait correctement")
                            
                            # Vérifier le contenu
                            with open(extracted_file, 'r') as f:
                                content = f.read()
                            
                            if "Fake game executable" in content:
                                print("✅ Contenu du fichier correct")
                            else:
                                print("❌ Contenu du fichier incorrect")
                        else:
                            print("❌ Fichier non extrait")
                        
                        # Vérifier que l'archive a été supprimée
                        if not os.path.exists(rar_file):
                            print("✅ Archive supprimée automatiquement !")
                            return True
                        else:
                            print("❌ Archive non supprimée")
                            return False
                    else:
                        print("❌ Extraction échouée")
                        return False
                        
                else:
                    print(f"❌ Erreur création archive: {result.stderr}")
                    return False
            else:
                print("❌ WinRAR non trouvé")
                return False
                
        except Exception as e:
            print(f"❌ Erreur test: {e}")
            return False

def extract_online_fix_archive_with_deletion(rar_file, extract_to):
    """Extrait une archive RAR online-fix avec suppression automatique"""
    try:
        print(f"🔍 Extraction de: {rar_file}")
        print(f"🔍 Vers: {extract_to}")
        
        # Mot de passe pour les archives online-fix
        password = "online-fix.me"
        print(f"🔑 Mot de passe: {password}")
        
        # Vérifier que le fichier RAR existe
        if not os.path.exists(rar_file):
            print(f"❌ Le fichier RAR n'existe pas: {rar_file}")
            return False
        
        print(f"✅ Fichier RAR existe, taille: {os.path.getsize(rar_file)} bytes")
        
        # Utiliser rarfile pour extraire
        print(f"🔍 Ouverture du fichier RAR...")
        with rarfile.RarFile(rar_file) as rf:
            print(f"✅ Fichier RAR ouvert avec succès")
            
            # Lister le contenu
            files = rf.namelist()
            print(f"📋 Contenu: {len(files)} fichier(s)")
            for filename in files:
                print(f"   - {filename}")
            
            # Configurer le mot de passe
            rf.setpassword(password)
            print(f"✅ Mot de passe configuré")
            
            # Extraire tous les fichiers
            print(f"🔓 Extraction en cours...")
            rf.extractall(extract_to)
            print(f"✅ Extraction terminée")
        
        # Supprimer l'archive après extraction réussie
        try:
            print(f"🗑️  Suppression de l'archive: {rar_file}")
            os.remove(rar_file)
            print(f"✅ Archive supprimée avec succès")
        except Exception as e:
            print(f"⚠️  Erreur suppression archive: {e}")
            # Ne pas faire échouer l'extraction si la suppression échoue
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'extraction: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("🚀 TEST SUPPRESSION AUTOMATIQUE DES ARCHIVES")
    print("=" * 70)
    print("Test de l'extraction avec suppression automatique de l'archive")
    print()
    
    success = test_extraction_with_deletion()
    
    print("\n" + "="*70)
    if success:
        print("✅ TEST RÉUSSI !")
        print("🎉 L'extraction fonctionne et l'archive est supprimée automatiquement")
        print("🔧 Cette fonctionnalité est maintenant intégrée au launcher")
    else:
        print("❌ TEST ÉCHOUÉ")
        print("🔧 Vérifiez WinRAR et les permissions de fichiers")
    
    print(f"\n💡 FONCTIONNEMENT:")
    print("1. Téléchargement du jeu online-fix")
    print("2. Extraction automatique avec mot de passe")
    print("3. Suppression automatique de l'archive RAR")
    print("4. Conservation des fichiers extraits seulement")
    
    return success

if __name__ == "__main__":
    success = main()
    input(f"\n{'✅ Test terminé' if success else '❌ Échec du test'} - Appuyez sur Entrée pour continuer...")
