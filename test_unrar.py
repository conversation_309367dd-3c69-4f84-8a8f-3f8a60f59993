#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test pour vérifier si UnRAR fonctionne mieux que WinRAR
"""

import os
import rarfile

def test_unrar_vs_winrar():
    """Test UnRAR vs WinRAR pour l'extraction"""
    
    print("🔍 TEST UNRAR VS WINRAR")
    print("=" * 50)
    
    # Chemins possibles
    unrar_path = r"C:\Program Files\WinRAR\UnRAR.exe"
    winrar_path = r"C:\Program Files\WinRAR\WinRAR.exe"
    
    print(f"Vérification des outils:")
    print(f"   UnRAR.exe: {'✅ Trouvé' if os.path.exists(unrar_path) else '❌ Non trouvé'}")
    print(f"   WinRAR.exe: {'✅ Trouvé' if os.path.exists(winrar_path) else '❌ Non trouvé'}")
    
    # Archive à tester
    archive_path = r"C:\Users\<USER>\Downloads\Content Warning\Content.Warning.v1.17.b-OFME.rar"
    
    if not os.path.exists(archive_path):
        print(f"❌ Archive non trouvée: {archive_path}")
        return
    
    print(f"\n📁 Archive à tester: {archive_path}")
    print(f"📊 Taille: {os.path.getsize(archive_path) / (1024*1024):.1f} MB")
    
    # Test avec UnRAR
    if os.path.exists(unrar_path):
        print(f"\n🧪 TEST AVEC UNRAR")
        print("-" * 30)
        
        try:
            rarfile.UNRAR_TOOL = unrar_path
            print(f"✅ Configuration: {rarfile.UNRAR_TOOL}")
            
            with rarfile.RarFile(archive_path) as rf:
                print(f"✅ Archive ouverte avec succès")
                
                # Lister le contenu
                files = rf.namelist()
                print(f"📋 Contenu: {len(files)} fichier(s)")
                
                if len(files) > 0:
                    for i, filename in enumerate(files[:5]):
                        print(f"   - {filename}")
                    if len(files) > 5:
                        print(f"   ... et {len(files) - 5} autres")
                    
                    # Test avec mot de passe
                    rf.setpassword("online-fix.me")
                    print(f"✅ Mot de passe configuré")
                    
                    # Test d'extraction d'un seul fichier
                    try:
                        first_file = files[0]
                        print(f"🔍 Test extraction de: {first_file}")
                        
                        import tempfile
                        with tempfile.TemporaryDirectory() as temp_dir:
                            rf.extract(first_file, temp_dir)
                            extracted_path = os.path.join(temp_dir, first_file)
                            if os.path.exists(extracted_path):
                                print(f"✅ Extraction réussie avec UnRAR !")
                                return True
                            else:
                                print(f"❌ Fichier non extrait")
                    except Exception as e:
                        print(f"❌ Erreur extraction: {e}")
                else:
                    print(f"⚠️  Archive vide ou protégée")
                    
        except Exception as e:
            print(f"❌ Erreur avec UnRAR: {e}")
    
    # Test avec WinRAR
    if os.path.exists(winrar_path):
        print(f"\n🧪 TEST AVEC WINRAR")
        print("-" * 30)
        
        try:
            rarfile.UNRAR_TOOL = winrar_path
            print(f"✅ Configuration: {rarfile.UNRAR_TOOL}")
            
            with rarfile.RarFile(archive_path) as rf:
                print(f"✅ Archive ouverte avec succès")
                
                # Lister le contenu
                files = rf.namelist()
                print(f"📋 Contenu: {len(files)} fichier(s)")
                
                if len(files) > 0:
                    for i, filename in enumerate(files[:5]):
                        print(f"   - {filename}")
                    if len(files) > 5:
                        print(f"   ... et {len(files) - 5} autres")
                    
                    # Test avec mot de passe
                    rf.setpassword("online-fix.me")
                    print(f"✅ Mot de passe configuré")
                    
                    # Test d'extraction d'un seul fichier
                    try:
                        first_file = files[0]
                        print(f"🔍 Test extraction de: {first_file}")
                        
                        import tempfile
                        with tempfile.TemporaryDirectory() as temp_dir:
                            rf.extract(first_file, temp_dir)
                            extracted_path = os.path.join(temp_dir, first_file)
                            if os.path.exists(extracted_path):
                                print(f"✅ Extraction réussie avec WinRAR !")
                                return True
                            else:
                                print(f"❌ Fichier non extrait")
                    except Exception as e:
                        print(f"❌ Erreur extraction: {e}")
                else:
                    print(f"⚠️  Archive vide ou protégée")
                    
        except Exception as e:
            print(f"❌ Erreur avec WinRAR: {e}")
    
    return False

def main():
    """Fonction principale"""
    
    print("🚀 TEST EXTRACTION CONTENT WARNING")
    print("=" * 70)
    print("Test pour résoudre le problème d'extraction")
    print()
    
    success = test_unrar_vs_winrar()
    
    print("\n" + "="*70)
    if success:
        print("✅ SOLUTION TROUVÉE !")
        print("L'extraction fonctionne avec l'un des outils")
    else:
        print("❌ PROBLÈME PERSISTANT")
        print("Aucun des outils ne fonctionne correctement")
    
    print(f"\n💡 RECOMMANDATIONS:")
    print("1. Utiliser UnRAR.exe si disponible (plus compatible)")
    print("2. Vérifier que l'archive n'est pas corrompue")
    print("3. Tester l'extraction manuelle avec WinRAR")
    
    return success

if __name__ == "__main__":
    success = main()
    input(f"\n{'✅ Test terminé' if success else '❌ Échec du test'} - Appuyez sur Entrée pour continuer...")
